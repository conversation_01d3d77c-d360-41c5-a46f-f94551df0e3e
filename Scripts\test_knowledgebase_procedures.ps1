# Test KnowledgeBase Stored Procedures
# This script runs comprehensive tests on the KnowledgeBase stored procedures
# to ensure they are working properly after database schema updates.

param(
    [string]$VenvPath = ".\venv",
    [switch]$Verbose = $false
)

Write-Host "🧪 KnowledgeBase Stored Procedures Test Runner" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan

# Check if virtual environment exists
$pythonExe = Join-Path $VenvPath "Scripts\python.exe"
if (-not (Test-Path $pythonExe)) {
    Write-Host "❌ Virtual environment not found at: $VenvPath" -ForegroundColor Red
    Write-Host "Please ensure the virtual environment is set up correctly." -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Using Python from: $pythonExe" -ForegroundColor Green

# Set environment variables if needed
$env:PYTHONPATH = $PWD

try {
    Write-Host "`n🚀 Starting KnowledgeBase stored procedure tests..." -ForegroundColor Yellow
    
    if ($Verbose) {
        Write-Host "Running in verbose mode..." -ForegroundColor Gray
        & $pythonExe "Tests\test_knowledgebase_procedures.py" -v
    } else {
        & $pythonExe "Tests\test_knowledgebase_procedures.py"
    }
    
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Host "`n🎉 All tests completed successfully!" -ForegroundColor Green
        Write-Host "KnowledgeBase stored procedures are working correctly." -ForegroundColor Green
    } else {
        Write-Host "`n⚠️  Tests completed with errors (Exit code: $exitCode)" -ForegroundColor Red
        Write-Host "Please review the test output above for details." -ForegroundColor Yellow
    }
    
    exit $exitCode
    
} catch {
    Write-Host "`n💥 Error running tests: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
