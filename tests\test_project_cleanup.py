"""
Test suite for project cleanup functionality
"""

import asyncio
import pytest
import sys
import os
import tempfile
import shutil
import json
from datetime import datetime
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Scripts.project_cleanup_manager import ProjectCleanupManager


class TestProjectCleanupManager:
    """Test suite for the project cleanup manager."""
    
    @pytest.fixture
    def temp_project_dir(self):
        """Create a temporary project directory for testing."""
        temp_dir = tempfile.mkdtemp()
        project_path = Path(temp_dir)
        
        # Create project structure
        (project_path / "logs").mkdir()
        (project_path / "Scripts").mkdir()
        (project_path / "__pycache__").mkdir()
        (project_path / "node_modules").mkdir()
        
        yield project_path
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_analysis_data(self, temp_project_dir):
        """Create sample analysis data for testing."""
        analysis_data = {
            "analysis_date": datetime.now().isoformat(),
            "project_root": str(temp_project_dir),
            "summary": {
                "total_files": 100,
                "total_size": 1000000,
                "duplicate_groups": 10,
                "obsolete_files": 20
            },
            "files": {
                "test1.py": {
                    "size": 1000,
                    "modified": datetime.now().isoformat(),
                    "hash": "abc123",
                    "extension": ".py",
                    "is_entry_point": False
                },
                "test2.py": {
                    "size": 1000,
                    "modified": datetime.now().isoformat(),
                    "hash": "abc123",
                    "extension": ".py",
                    "is_entry_point": False
                }
            },
            "duplicates": {
                "abc123": ["test1.py", "test2.py"]
            },
            "obsolete_files": ["old_file.txt", "temp_file.tmp"]
        }
        
        # Save analysis data
        analysis_file = temp_project_dir / "Scripts" / "project_analysis_report.json"
        analysis_file.parent.mkdir(exist_ok=True)
        with open(analysis_file, 'w') as f:
            json.dump(analysis_data, f)
        
        return analysis_data
    
    @pytest.fixture
    def cleanup_manager(self, temp_project_dir, sample_analysis_data):
        """Create a cleanup manager instance for testing."""
        analysis_file = temp_project_dir / "Scripts" / "project_analysis_report.json"
        manager = ProjectCleanupManager(temp_project_dir, analysis_file)
        manager.dry_run = True  # Always dry run for tests
        return manager
    
    def test_cleanup_manager_initialization(self, cleanup_manager, temp_project_dir):
        """Test that the cleanup manager initializes correctly."""
        assert cleanup_manager.project_root == temp_project_dir
        assert cleanup_manager.dry_run is True
        assert cleanup_manager.analysis_data is not None
        assert cleanup_manager.files_moved == 0
        assert cleanup_manager.files_deleted == 0
    
    def test_create_archive_structure(self, cleanup_manager, temp_project_dir):
        """Test creating archive directory structure."""
        cleanup_manager.create_archive_structure()
        
        # Check that archive directories were created
        expected_dirs = [
            temp_project_dir / "Archive" / "duplicates",
            temp_project_dir / "Archive" / "obsolete",
            temp_project_dir / "Archive" / "cache",
            temp_project_dir / "Archive" / "temp",
            temp_project_dir / "Archive" / "logs",
            temp_project_dir / "Backup"
        ]
        
        for dir_path in expected_dirs:
            assert dir_path.exists()
            assert dir_path.is_dir()
    
    def test_cleanup_cache_directories(self, cleanup_manager, temp_project_dir):
        """Test cache directory cleanup."""
        # Create some cache files and directories
        cache_dir = temp_project_dir / "__pycache__"
        cache_dir.mkdir(exist_ok=True)
        (cache_dir / "test.pyc").write_text("cache content")
        
        pytest_cache = temp_project_dir / ".pytest_cache"
        pytest_cache.mkdir()
        (pytest_cache / "test_cache").write_text("pytest cache")
        
        # Run cache cleanup (dry run)
        initial_files_deleted = cleanup_manager.files_deleted
        cleanup_manager.cleanup_cache_directories()
        
        # In dry run mode, files should still exist but counters should be updated
        assert cache_dir.exists()  # Still exists in dry run
        assert cleanup_manager.files_deleted > initial_files_deleted
    
    def test_archive_duplicate_files(self, cleanup_manager, temp_project_dir):
        """Test duplicate file archiving."""
        # Create the duplicate files mentioned in sample data
        (temp_project_dir / "test1.py").write_text("duplicate content")
        (temp_project_dir / "test2.py").write_text("duplicate content")
        
        # Run duplicate archiving (dry run)
        initial_files_moved = cleanup_manager.files_moved
        cleanup_manager.archive_duplicate_files()
        
        # In dry run mode, files should still exist but counters should be updated
        assert (temp_project_dir / "test1.py").exists()
        assert (temp_project_dir / "test2.py").exists()
        assert cleanup_manager.files_moved > initial_files_moved
    
    def test_archive_obsolete_files(self, cleanup_manager, temp_project_dir):
        """Test obsolete file archiving."""
        # Create the obsolete files mentioned in sample data
        (temp_project_dir / "old_file.txt").write_text("old content")
        (temp_project_dir / "temp_file.tmp").write_text("temp content")
        
        # Run obsolete file archiving (dry run)
        initial_files_moved = cleanup_manager.files_moved
        cleanup_manager.archive_obsolete_files(max_files=5)
        
        # In dry run mode, files should still exist but counters should be updated
        assert (temp_project_dir / "old_file.txt").exists()
        assert (temp_project_dir / "temp_file.tmp").exists()
        assert cleanup_manager.files_moved > initial_files_moved
    
    def test_cleanup_large_log_files(self, cleanup_manager, temp_project_dir):
        """Test large log file cleanup."""
        # Create a large log file
        logs_dir = temp_project_dir / "logs"
        logs_dir.mkdir(exist_ok=True)
        large_log = logs_dir / "large.log"
        
        # Create a file larger than 10MB (simulated with smaller size for testing)
        large_content = "x" * (11 * 1024 * 1024)  # 11MB
        large_log.write_text(large_content)
        
        # Run log cleanup (dry run)
        initial_files_moved = cleanup_manager.files_moved
        cleanup_manager.cleanup_large_log_files(max_size_mb=10)
        
        # In dry run mode, file should still exist but counters should be updated
        assert large_log.exists()
        assert cleanup_manager.files_moved > initial_files_moved
    
    def test_generate_cleanup_report(self, cleanup_manager, temp_project_dir):
        """Test cleanup report generation."""
        # Set some test values
        cleanup_manager.files_moved = 10
        cleanup_manager.files_deleted = 5
        cleanup_manager.space_saved = 1000000
        
        # Generate report
        cleanup_manager.generate_cleanup_report()
        
        # Check that report file was created
        report_file = temp_project_dir / "logs" / "project_cleanup_report.json"
        assert report_file.exists()
        
        # Check report content
        with open(report_file, 'r') as f:
            report = json.load(f)
        
        assert report['dry_run'] is True
        assert report['summary']['files_moved'] == 10
        assert report['summary']['files_deleted'] == 5
        assert report['summary']['space_saved_bytes'] == 1000000
    
    def test_run_safe_cleanup(self, cleanup_manager, temp_project_dir):
        """Test the complete safe cleanup process."""
        # Create test files and directories
        (temp_project_dir / "__pycache__").mkdir(exist_ok=True)
        (temp_project_dir / "__pycache__" / "test.pyc").write_text("cache")
        (temp_project_dir / "test1.py").write_text("duplicate content")
        (temp_project_dir / "test2.py").write_text("duplicate content")
        
        # Run safe cleanup
        cleanup_manager.run_safe_cleanup(['cache', 'duplicates'])
        
        # Check that archive structure was created
        assert (temp_project_dir / "Archive").exists()
        assert (temp_project_dir / "Archive" / "duplicates").exists()
        
        # Check that report was generated
        report_file = temp_project_dir / "logs" / "project_cleanup_report.json"
        assert report_file.exists()


async def run_project_cleanup_tests():
    """Run all project cleanup tests."""
    print("🧪 Project Cleanup Test Suite")
    print("=" * 50)
    
    # Test the cleanup manager with a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create sample project structure
        (temp_path / "Scripts").mkdir()
        (temp_path / "logs").mkdir()
        (temp_path / "__pycache__").mkdir()
        
        # Create sample analysis data
        analysis_data = {
            "analysis_date": datetime.now().isoformat(),
            "project_root": str(temp_path),
            "summary": {
                "total_files": 50,
                "total_size": 500000,
                "duplicate_groups": 5,
                "obsolete_files": 10
            },
            "files": {
                "test1.py": {"size": 1000, "hash": "abc123"},
                "test2.py": {"size": 1000, "hash": "abc123"}
            },
            "duplicates": {
                "abc123": ["test1.py", "test2.py"]
            },
            "obsolete_files": ["old_file.txt"]
        }
        
        analysis_file = temp_path / "Scripts" / "project_analysis_report.json"
        with open(analysis_file, 'w') as f:
            json.dump(analysis_data, f)
        
        # Test cleanup manager initialization
        manager = ProjectCleanupManager(temp_path, analysis_file)
        manager.dry_run = True
        print("✅ Manager initialization: PASS")
        
        # Test archive structure creation
        manager.create_archive_structure()
        assert (temp_path / "Archive" / "duplicates").exists()
        print("✅ Archive structure creation: PASS")
        
        # Test cache cleanup
        cache_file = temp_path / "__pycache__" / "test.pyc"
        cache_file.parent.mkdir(exist_ok=True)
        cache_file.write_text("cache content")
        
        initial_deleted = manager.files_deleted
        manager.cleanup_cache_directories()
        assert manager.files_deleted > initial_deleted
        print("✅ Cache cleanup: PASS")
        
        # Test report generation
        manager.generate_cleanup_report()
        report_file = temp_path / "logs" / "project_cleanup_report.json"
        assert report_file.exists()
        print("✅ Report generation: PASS")
    
    print("\n🎉 Project cleanup tests completed!")
    return True


if __name__ == "__main__":
    asyncio.run(run_project_cleanup_tests())
