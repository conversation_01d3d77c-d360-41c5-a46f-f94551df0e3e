# Emergency-Shutdown.ps1
# Emergency shutdown script for N8N_Builder system
# This script forcefully terminates all N8N_Builder related processes

param(
    [switch]$Force,
    [switch]$Verbose
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "N8N_Builder Emergency Shutdown" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

function Stop-ProcessesByPort {
    param([int[]]$Ports)
    
    foreach ($port in $Ports) {
        try {
            $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
            foreach ($conn in $connections) {
                $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Host "[INFO] Killing process $($process.Name) (PID: $($process.Id)) on port $port" -ForegroundColor Yellow
                    Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
                }
            }
        }
        catch {
            if ($Verbose) {
                Write-Host "[DEBUG] No processes found on port $port" -ForegroundColor Gray
            }
        }
    }
}

function Stop-ProcessesByName {
    param([string[]]$ProcessNames)
    
    foreach ($name in $ProcessNames) {
        try {
            $processes = Get-Process -Name $name -ErrorAction SilentlyContinue
            foreach ($process in $processes) {
                # Check if it's related to N8N_Builder
                $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
                if ($commandLine -and ($commandLine -like "*run.py*" -or $commandLine -like "*n8n_builder*" -or $commandLine -like "*Self-Healer*")) {
                    Write-Host "[INFO] Killing N8N_Builder process: $($process.Name) (PID: $($process.Id))" -ForegroundColor Yellow
                    Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
                }
            }
        }
        catch {
            if ($Verbose) {
                Write-Host "[DEBUG] No $name processes found" -ForegroundColor Gray
            }
        }
    }
}

# Main shutdown sequence
try {
    Write-Host "[INFO] Starting emergency shutdown sequence..." -ForegroundColor Green
    
    # Stop processes by port (N8N_Builder typically uses these ports)
    Write-Host "[INFO] Freeing ports 8002, 8003, 8081..." -ForegroundColor Green
    Stop-ProcessesByPort -Ports @(8002, 8003, 8081)
    
    # Stop Python processes that might be running N8N_Builder
    Write-Host "[INFO] Stopping N8N_Builder Python processes..." -ForegroundColor Green
    Stop-ProcessesByName -ProcessNames @("python", "pythonw")
    
    # Give processes a moment to terminate (reduced for automated execution)
    Start-Sleep -Seconds 1
    
    # Force kill any remaining processes if -Force is specified
    if ($Force) {
        Write-Host "[INFO] Force killing any remaining processes..." -ForegroundColor Red
        
        # Force kill by port again
        Stop-ProcessesByPort -Ports @(8002, 8003, 8081)
        
        # Force kill any Python processes (be careful with this)
        if ($Force) {
            Write-Host "[WARNING] Force killing ALL Python processes..." -ForegroundColor Red
            Get-Process -Name "python*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
        }
    }
    
    Write-Host ""
    Write-Host "[SUCCESS] Emergency shutdown complete!" -ForegroundColor Green
    Write-Host "[INFO] All N8N_Builder processes have been terminated." -ForegroundColor Cyan
    Write-Host "[INFO] You can now safely restart the system with: python run.py" -ForegroundColor Cyan
    
    # Verify ports are free
    Write-Host ""
    Write-Host "[INFO] Verifying ports are free..." -ForegroundColor Green
    $portsToCheck = @(8002, 8003, 8081)
    foreach ($port in $portsToCheck) {
        $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Host "[WARNING] Port $port is still in use" -ForegroundColor Yellow
        } else {
            Write-Host "[OK] Port $port is free" -ForegroundColor Green
        }
    }
}
catch {
    Write-Host "[WARNING] Emergency shutdown encountered an issue: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "[INFO] Continuing with startup anyway..." -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Emergency shutdown completed." -ForegroundColor Green
