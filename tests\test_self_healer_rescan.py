#!/usr/bin/env python3
"""
Test script to force rescan of Self-Healer logs and check error detection.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from Self_Healer.core.error_monitor import ErrorMonitor


async def test_rescan():
    """Test forced rescan of logs."""
    print("=== Testing Self-Healer Log Rescan ===")
    
    # Initialize error monitor
    log_directory = Path("logs")
    error_monitor = ErrorMonitor(log_directory)
    
    print(f"Log directory: {log_directory.absolute()}")
    print()
    
    # Start error monitor
    await error_monitor.start()
    
    print("=== Before Rescan ===")
    print(f"Detected errors: {len(error_monitor.detected_errors)}")
    
    # Force rescan for the last 24 hours
    print("\n=== Forcing Rescan (24 hours) ===")
    new_errors_found = await error_monitor.force_rescan_logs(hours_back=24)
    
    print(f"New errors found during rescan: {new_errors_found}")
    print(f"Total detected errors after rescan: {len(error_monitor.detected_errors)}")
    
    # Show detected errors
    if error_monitor.detected_errors:
        print("\n=== Detected Errors ===")
        for error_id, detected_error in error_monitor.detected_errors.items():
            print(f"Error ID: {error_id}")
            print(f"  Timestamp: {detected_error.timestamp}")
            print(f"  Severity: {detected_error.severity}")
            print(f"  Category: {detected_error.category}")
            print(f"  Frequency: {detected_error.frequency}")
            print(f"  Message: {detected_error.error_detail.message}")
            print()
    
    # Check for new errors
    print("=== New Errors Check ===")
    new_errors = await error_monitor.get_new_errors()
    print(f"New errors ready for healing: {len(new_errors)}")
    
    for i, error in enumerate(new_errors):
        print(f"  New Error {i+1}:")
        print(f"    Title: {error.title}")
        print(f"    Category: {error.category}")
        print(f"    Severity: {error.severity}")
        print(f"    Message: {error.message}")
        print()
    
    await error_monitor.stop()
    print("=== Test Complete ===")


if __name__ == "__main__":
    asyncio.run(test_rescan())
