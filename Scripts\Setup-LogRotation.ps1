# Setup-LogRotation.ps1
# PowerShell script to set up 24-hour log rotation for N8N Builder

param(
    [switch]$Setup,
    [switch]$Test,
    [switch]$Status,
    [int]$Cleanup = 0,
    [switch]$Install,
    [switch]$Help
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$VenvPath = Join-Path $ProjectRoot "venv"
$PythonExe = Join-Path $VenvPath "Scripts\python.exe"
$LogRotationScript = Join-Path $ScriptDir "setup_log_rotation.py"

function Show-Help {
    Write-Host "🔄 N8N Builder Log Rotation Setup" -ForegroundColor Cyan
    Write-Host "=================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor Yellow
    Write-Host "  .\Setup-LogRotation.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor Yellow
    Write-Host "  -Setup      Set up 24-hour log rotation system"
    Write-Host "  -Test       Test the log rotation system"
    Write-Host "  -Status     Show current log rotation status"
    Write-Host "  -Cleanup N  Clean up logs older than N days"
    Write-Host "  -Install    Install required dependencies"
    Write-Host "  -Help       Show this help message"
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Yellow
    Write-Host "  .\Setup-LogRotation.ps1 -Setup"
    Write-Host "  .\Setup-LogRotation.ps1 -Status"
    Write-Host "  .\Setup-LogRotation.ps1 -Cleanup 30"
    Write-Host "  .\Setup-LogRotation.ps1 -Install"
    Write-Host ""
    Write-Host "FEATURES:" -ForegroundColor Green
    Write-Host "  ✅ 24-hour rotation at midnight"
    Write-Host "  ✅ Datestamped backup files"
    Write-Host "  ✅ Automatic compression"
    Write-Host "  ✅ 30-day retention policy"
    Write-Host "  ✅ Emergency size-based rotation"
    Write-Host "  ✅ Multiple log file support"
}

function Test-Prerequisites {
    Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow
    
    # Check if virtual environment exists
    if (-not (Test-Path $VenvPath)) {
        Write-Host "❌ Virtual environment not found at: $VenvPath" -ForegroundColor Red
        Write-Host "   Please run setup.py or create virtual environment first" -ForegroundColor Red
        return $false
    }
    
    # Check if Python executable exists
    if (-not (Test-Path $PythonExe)) {
        Write-Host "❌ Python executable not found at: $PythonExe" -ForegroundColor Red
        return $false
    }
    
    # Check if log rotation script exists
    if (-not (Test-Path $LogRotationScript)) {
        Write-Host "❌ Log rotation script not found at: $LogRotationScript" -ForegroundColor Red
        return $false
    }
    
    Write-Host "✅ Prerequisites check passed" -ForegroundColor Green
    return $true
}

function Install-Dependencies {
    Write-Host "📦 Installing log rotation dependencies..." -ForegroundColor Yellow
    
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    try {
        # Install schedule package
        Write-Host "Installing schedule package..." -ForegroundColor Cyan
        & $PythonExe -m pip install schedule>=1.2.0
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error installing dependencies: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Invoke-LogRotationSetup {
    Write-Host "🔄 Setting up 24-hour log rotation..." -ForegroundColor Yellow
    
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    try {
        & $PythonExe $LogRotationScript --setup
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Log rotation setup completed successfully" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Log rotation setup failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error during setup: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Invoke-LogRotationTest {
    Write-Host "🧪 Testing log rotation system..." -ForegroundColor Yellow
    
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    try {
        & $PythonExe $LogRotationScript --test
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Log rotation test completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Log rotation test failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error during test: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-LogRotationStatus {
    Write-Host "📊 Checking log rotation status..." -ForegroundColor Yellow
    
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    try {
        & $PythonExe $LogRotationScript --status
        
        if ($LASTEXITCODE -eq 0) {
            return $true
        } else {
            Write-Host "❌ Failed to get log rotation status" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error getting status: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Invoke-LogCleanup {
    param([int]$Days)
    
    Write-Host "🧹 Cleaning up logs older than $Days days..." -ForegroundColor Yellow
    
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    try {
        & $PythonExe $LogRotationScript --cleanup $Days
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Log cleanup completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Log cleanup failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error during cleanup: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-LogDirectory {
    $LogsDir = Join-Path $ProjectRoot "logs"
    
    if (Test-Path $LogsDir) {
        Write-Host "📁 Log Directory: $LogsDir" -ForegroundColor Cyan
        
        $LogFiles = Get-ChildItem $LogsDir -Filter "*.log" | Sort-Object LastWriteTime -Descending
        $BackupFiles = Get-ChildItem $LogsDir -Filter "*.log.*" | Sort-Object LastWriteTime -Descending
        
        if ($LogFiles.Count -gt 0) {
            Write-Host "📄 Current Log Files:" -ForegroundColor Green
            foreach ($file in $LogFiles) {
                $sizeMB = [math]::Round($file.Length / 1MB, 2)
                Write-Host "   $($file.Name) - ${sizeMB}MB - $($file.LastWriteTime)" -ForegroundColor White
            }
        }
        
        if ($BackupFiles.Count -gt 0) {
            Write-Host "📦 Backup Files: $($BackupFiles.Count)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "📁 Log directory not found: $LogsDir" -ForegroundColor Yellow
    }
}

# Main execution logic
function Main {
    Write-Host "🔄 N8N Builder Log Rotation Manager" -ForegroundColor Cyan
    Write-Host "===================================" -ForegroundColor Cyan
    Write-Host ""
    
    # Show help if requested
    if ($Help) {
        Show-Help
        return
    }
    
    # Install dependencies if requested
    if ($Install) {
        if (Install-Dependencies) {
            Write-Host ""
            Write-Host "🎉 Dependencies installed successfully!" -ForegroundColor Green
            Write-Host "You can now run: .\Setup-LogRotation.ps1 -Setup" -ForegroundColor Cyan
        }
        return
    }
    
    # Execute based on parameters
    if ($Setup) {
        if (Invoke-LogRotationSetup) {
            Write-Host ""
            Show-LogDirectory
        }
    }
    elseif ($Test) {
        Invoke-LogRotationTest
    }
    elseif ($Status) {
        Show-LogRotationStatus
        Write-Host ""
        Show-LogDirectory
    }
    elseif ($Cleanup -gt 0) {
        Invoke-LogCleanup -Days $Cleanup
    }
    else {
        # Default: Show status and help
        Write-Host "No action specified. Showing current status..." -ForegroundColor Yellow
        Write-Host ""
        Show-LogRotationStatus
        Write-Host ""
        Show-LogDirectory
        Write-Host ""
        Write-Host "💡 Use -Help to see available options" -ForegroundColor Cyan
    }
}

# Run the main function
Main
