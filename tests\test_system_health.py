"""
Comprehensive System Health Test Suite
Tests database connectivity, network processes, Self-Healer integration, and overall system health
"""

import asyncio
import pytest
import sys
import os
import psutil
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from n8n_builder.mcp_database_tool import MCPDatabaseTool
from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
from n8n_builder.config import Config
from n8n_builder.mcp_research_tool import MCPResearchTool


class SystemHealthChecker:
    """Comprehensive system health checker for N8N Builder and Self-Healer."""
    
    def __init__(self):
        self.config = Config()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'overall_status': 'UNKNOWN',
            'critical_failures': [],
            'warnings': []
        }
    
    async def run_all_health_checks(self) -> Dict[str, Any]:
        """Run all system health checks and return comprehensive results."""
        print("🏥 N8N Builder System Health Check")
        print("=" * 60)
        
        # Database connectivity tests
        await self._test_database_connectivity()
        
        # Network process validation
        await self._test_network_processes()
        
        # Self-Healer integration tests
        await self._test_selfhealer_integration()
        
        # System resource checks
        await self._test_system_resources()
        
        # Configuration validation
        await self._test_configuration()
        
        # File system health
        await self._test_filesystem_health()
        
        # Calculate overall status
        self._calculate_overall_status()
        
        return self.test_results
    
    async def _test_database_connectivity(self):
        """Test database connectivity and stored procedures."""
        print("\n🗄️ Database Connectivity Tests")
        print("-" * 40)
        
        test_name = "database_connectivity"
        self.test_results['tests'][test_name] = {
            'status': 'RUNNING',
            'details': {},
            'start_time': datetime.now().isoformat()
        }
        
        try:
            # Test MCP Database Tool
            db_tool = MCPDatabaseTool('knowledgebase')
            connection_result = await db_tool.test_connection()
            
            if connection_result['status'] == 'success':
                print(f"✅ Database Connection: {connection_result['database_name']}")
                
                # Test stored procedures
                knowledge_db = SelfHealerKnowledgeDB('knowledgebase')
                
                # Test basic procedure call
                test_result = await knowledge_db.get_solution_analytics(limit=1)
                
                if test_result['status'] == 'success':
                    print("✅ Stored Procedures: Working")
                    self.test_results['tests'][test_name]['status'] = 'PASS'
                else:
                    print(f"⚠️ Stored Procedures: {test_result.get('error', 'Unknown error')}")
                    self.test_results['tests'][test_name]['status'] = 'WARNING'
                    self.test_results['warnings'].append(f"Stored procedures issue: {test_result.get('error')}")
                
                self.test_results['tests'][test_name]['details'] = {
                    'database_name': connection_result['database_name'],
                    'server_version': connection_result['server_version'][:100],
                    'connection_time': connection_result['connection_time'],
                    'stored_procedures_working': test_result['status'] == 'success'
                }
            else:
                print(f"❌ Database Connection Failed: {connection_result}")
                self.test_results['tests'][test_name]['status'] = 'FAIL'
                self.test_results['critical_failures'].append(f"Database connection failed: {connection_result}")
                
        except Exception as e:
            print(f"❌ Database Test Exception: {e}")
            self.test_results['tests'][test_name]['status'] = 'FAIL'
            self.test_results['critical_failures'].append(f"Database test exception: {str(e)}")
        
        self.test_results['tests'][test_name]['end_time'] = datetime.now().isoformat()
    
    async def _test_network_processes(self):
        """Test network processes and port availability."""
        print("\n🌐 Network Process Validation")
        print("-" * 40)
        
        test_name = "network_processes"
        self.test_results['tests'][test_name] = {
            'status': 'RUNNING',
            'details': {},
            'start_time': datetime.now().isoformat()
        }
        
        try:
            # Check for common N8N Builder ports
            expected_ports = [1234, 5678, 8000, 3000]  # LM Studio, N8N Builder, FastAPI, etc.
            port_status = {}
            
            for port in expected_ports:
                connections = psutil.net_connections()
                port_in_use = any(conn.laddr.port == port for conn in connections if conn.laddr)
                port_status[port] = port_in_use
                
                if port_in_use:
                    print(f"✅ Port {port}: In use")
                else:
                    print(f"ℹ️ Port {port}: Available")
            
            # Check for N8N Docker processes
            n8n_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and ('n8n' in proc.info['name'].lower() or 
                                            'docker' in proc.info['name'].lower()):
                        n8n_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            print(f"ℹ️ Found {len(n8n_processes)} N8N/Docker processes")
            
            self.test_results['tests'][test_name]['status'] = 'PASS'
            self.test_results['tests'][test_name]['details'] = {
                'port_status': port_status,
                'n8n_processes': len(n8n_processes),
                'process_details': n8n_processes[:3]  # Limit to first 3 for brevity
            }
            
        except Exception as e:
            print(f"❌ Network Process Test Exception: {e}")
            self.test_results['tests'][test_name]['status'] = 'FAIL'
            self.test_results['critical_failures'].append(f"Network process test exception: {str(e)}")
        
        self.test_results['tests'][test_name]['end_time'] = datetime.now().isoformat()
    
    async def _test_selfhealer_integration(self):
        """Test Self-Healer integration and functionality."""
        print("\n🤖 Self-Healer Integration Tests")
        print("-" * 40)
        
        test_name = "selfhealer_integration"
        self.test_results['tests'][test_name] = {
            'status': 'RUNNING',
            'details': {},
            'start_time': datetime.now().isoformat()
        }
        
        try:
            # Check Self-Healer directory structure
            selfhealer_path = Path("Self_Healer")
            required_components = [
                "core/knowledge_integration.py",
                "core/knowledge_database_wrapper.py",
                "api/knowledge_endpoints.py",
                "config/config.yaml"
            ]
            
            component_status = {}
            for component in required_components:
                component_path = selfhealer_path / component
                component_status[component] = component_path.exists()
                
                if component_path.exists():
                    print(f"✅ Component: {component}")
                else:
                    print(f"❌ Missing: {component}")
            
            # Test knowledge database wrapper
            try:
                knowledge_db = SelfHealerKnowledgeDB('knowledgebase')
                search_result = await knowledge_db.search_knowledge("test", limit=1)
                
                if search_result['status'] == 'success':
                    print("✅ Knowledge Database Wrapper: Working")
                    wrapper_working = True
                else:
                    print(f"⚠️ Knowledge Database Wrapper: {search_result.get('error', 'Unknown error')}")
                    wrapper_working = False
                    self.test_results['warnings'].append(f"Knowledge wrapper issue: {search_result.get('error')}")
                    
            except Exception as e:
                print(f"❌ Knowledge Database Wrapper Exception: {e}")
                wrapper_working = False
                self.test_results['warnings'].append(f"Knowledge wrapper exception: {str(e)}")
            
            missing_components = [comp for comp, exists in component_status.items() if not exists]
            
            if missing_components:
                self.test_results['tests'][test_name]['status'] = 'WARNING'
                self.test_results['warnings'].append(f"Missing Self-Healer components: {missing_components}")
            else:
                self.test_results['tests'][test_name]['status'] = 'PASS'
            
            self.test_results['tests'][test_name]['details'] = {
                'component_status': component_status,
                'missing_components': missing_components,
                'knowledge_wrapper_working': wrapper_working
            }
            
        except Exception as e:
            print(f"❌ Self-Healer Integration Test Exception: {e}")
            self.test_results['tests'][test_name]['status'] = 'FAIL'
            self.test_results['critical_failures'].append(f"Self-Healer integration test exception: {str(e)}")
        
        self.test_results['tests'][test_name]['end_time'] = datetime.now().isoformat()
    
    async def _test_system_resources(self):
        """Test system resource availability and usage."""
        print("\n💻 System Resource Checks")
        print("-" * 40)
        
        test_name = "system_resources"
        self.test_results['tests'][test_name] = {
            'status': 'RUNNING',
            'details': {},
            'start_time': datetime.now().isoformat()
        }
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_gb = memory.available / (1024**3)
            
            # Disk usage
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024**3)
            
            print(f"ℹ️ CPU Usage: {cpu_percent:.1f}%")
            print(f"ℹ️ Memory Usage: {memory_percent:.1f}% (Available: {memory_available_gb:.1f} GB)")
            print(f"ℹ️ Disk Usage: {disk_percent:.1f}% (Free: {disk_free_gb:.1f} GB)")
            
            # Check thresholds
            warnings = []
            if cpu_percent > 80:
                warnings.append(f"High CPU usage: {cpu_percent:.1f}%")
            if memory_percent > 85:
                warnings.append(f"High memory usage: {memory_percent:.1f}%")
            if disk_percent > 90:
                warnings.append(f"High disk usage: {disk_percent:.1f}%")
            if memory_available_gb < 1:
                warnings.append(f"Low available memory: {memory_available_gb:.1f} GB")
            if disk_free_gb < 5:
                warnings.append(f"Low disk space: {disk_free_gb:.1f} GB")
            
            if warnings:
                print("⚠️ Resource warnings:")
                for warning in warnings:
                    print(f"   - {warning}")
                self.test_results['tests'][test_name]['status'] = 'WARNING'
                self.test_results['warnings'].extend(warnings)
            else:
                print("✅ System resources: Normal")
                self.test_results['tests'][test_name]['status'] = 'PASS'
            
            self.test_results['tests'][test_name]['details'] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_available_gb': memory_available_gb,
                'disk_percent': disk_percent,
                'disk_free_gb': disk_free_gb,
                'warnings': warnings
            }
            
        except Exception as e:
            print(f"❌ System Resource Test Exception: {e}")
            self.test_results['tests'][test_name]['status'] = 'FAIL'
            self.test_results['critical_failures'].append(f"System resource test exception: {str(e)}")
        
        self.test_results['tests'][test_name]['end_time'] = datetime.now().isoformat()

    async def _test_configuration(self):
        """Test configuration files and settings."""
        print("\n⚙️ Configuration Validation")
        print("-" * 40)

        test_name = "configuration"
        self.test_results['tests'][test_name] = {
            'status': 'RUNNING',
            'details': {},
            'start_time': datetime.now().isoformat()
        }

        try:
            # Check main configuration
            config_issues = []

            # Test MCP database configuration
            if hasattr(self.config, 'mcp_database') and self.config.mcp_database.enabled:
                if 'knowledgebase' in self.config.mcp_database.connections:
                    print("✅ MCP Database Config: Found")
                else:
                    config_issues.append("MCP Database knowledgebase connection not configured")
                    print("❌ MCP Database Config: Missing knowledgebase connection")
            else:
                config_issues.append("MCP Database not enabled or configured")
                print("❌ MCP Database Config: Not enabled")

            # Test MCP research configuration
            if hasattr(self.config, 'mcp_research') and self.config.mcp_research.enabled:
                print("✅ MCP Research Config: Enabled")
            else:
                config_issues.append("MCP Research not enabled")
                print("⚠️ MCP Research Config: Not enabled")

            # Check environment files
            env_files = ['.env', 'n8n-docker/.env', 'Self_Healer/config/.env']
            env_status = {}

            for env_file in env_files:
                env_path = Path(env_file)
                env_status[env_file] = env_path.exists()
                if env_path.exists():
                    print(f"✅ Environment file: {env_file}")
                else:
                    print(f"ℹ️ Environment file not found: {env_file}")

            if config_issues:
                self.test_results['tests'][test_name]['status'] = 'WARNING'
                self.test_results['warnings'].extend(config_issues)
            else:
                self.test_results['tests'][test_name]['status'] = 'PASS'

            self.test_results['tests'][test_name]['details'] = {
                'config_issues': config_issues,
                'env_files': env_status,
                'mcp_database_enabled': hasattr(self.config, 'mcp_database') and self.config.mcp_database.enabled,
                'mcp_research_enabled': hasattr(self.config, 'mcp_research') and self.config.mcp_research.enabled
            }

        except Exception as e:
            print(f"❌ Configuration Test Exception: {e}")
            self.test_results['tests'][test_name]['status'] = 'FAIL'
            self.test_results['critical_failures'].append(f"Configuration test exception: {str(e)}")

        self.test_results['tests'][test_name]['end_time'] = datetime.now().isoformat()

    async def _test_filesystem_health(self):
        """Test file system health and project structure."""
        print("\n📁 File System Health")
        print("-" * 40)

        test_name = "filesystem_health"
        self.test_results['tests'][test_name] = {
            'status': 'RUNNING',
            'details': {},
            'start_time': datetime.now().isoformat()
        }

        try:
            # Check critical directories
            critical_dirs = [
                "n8n_builder",
                "Self_Healer",
                "tests",
                "logs",
                "projects"
            ]

            dir_status = {}
            missing_dirs = []

            for directory in critical_dirs:
                dir_path = Path(directory)
                exists = dir_path.exists()
                dir_status[directory] = exists

                if exists:
                    print(f"✅ Directory: {directory}")
                else:
                    missing_dirs.append(directory)
                    print(f"❌ Missing directory: {directory}")

            # Check log files and rotation
            logs_dir = Path("logs")
            log_files = []
            large_logs = []

            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    size_mb = log_file.stat().st_size / (1024 * 1024)
                    log_files.append({
                        'name': log_file.name,
                        'size_mb': round(size_mb, 2)
                    })

                    if size_mb > 100:  # Files larger than 100MB
                        large_logs.append(f"{log_file.name} ({size_mb:.1f} MB)")

                print(f"ℹ️ Found {len(log_files)} log files")
                if large_logs:
                    print(f"⚠️ Large log files: {', '.join(large_logs)}")
                    self.test_results['warnings'].append(f"Large log files found: {large_logs}")

            # Count total files (for the 5000+ file investigation)
            total_files = sum(1 for _ in Path(".").rglob("*") if _.is_file())
            print(f"ℹ️ Total files in project: {total_files}")

            if total_files > 5000:
                self.test_results['warnings'].append(f"High file count detected: {total_files} files")
                print(f"⚠️ High file count: {total_files} files")

            # Check for cache directories that might be accumulating files
            cache_dirs = ["__pycache__", "cache", ".pytest_cache", "node_modules"]
            cache_status = {}

            for cache_dir in cache_dirs:
                cache_paths = list(Path(".").rglob(cache_dir))
                cache_status[cache_dir] = len(cache_paths)
                if cache_paths:
                    print(f"ℹ️ Found {len(cache_paths)} {cache_dir} directories")

            if missing_dirs:
                self.test_results['tests'][test_name]['status'] = 'WARNING'
                self.test_results['warnings'].append(f"Missing critical directories: {missing_dirs}")
            else:
                self.test_results['tests'][test_name]['status'] = 'PASS'

            self.test_results['tests'][test_name]['details'] = {
                'directory_status': dir_status,
                'missing_directories': missing_dirs,
                'log_files': log_files,
                'large_logs': large_logs,
                'total_files': total_files,
                'cache_directories': cache_status
            }

        except Exception as e:
            print(f"❌ File System Test Exception: {e}")
            self.test_results['tests'][test_name]['status'] = 'FAIL'
            self.test_results['critical_failures'].append(f"File system test exception: {str(e)}")

        self.test_results['tests'][test_name]['end_time'] = datetime.now().isoformat()

    def _calculate_overall_status(self):
        """Calculate overall system health status."""
        if self.test_results['critical_failures']:
            self.test_results['overall_status'] = 'CRITICAL'
        elif self.test_results['warnings']:
            self.test_results['overall_status'] = 'WARNING'
        else:
            # Check if all tests passed
            all_passed = all(
                test['status'] == 'PASS' 
                for test in self.test_results['tests'].values()
            )
            self.test_results['overall_status'] = 'HEALTHY' if all_passed else 'WARNING'
    
    def print_summary(self):
        """Print a summary of all test results."""
        print("\n📊 System Health Summary")
        print("=" * 60)
        
        status_emoji = {
            'HEALTHY': '✅',
            'WARNING': '⚠️',
            'CRITICAL': '❌',
            'UNKNOWN': '❓'
        }
        
        overall_status = self.test_results['overall_status']
        print(f"{status_emoji.get(overall_status, '❓')} Overall Status: {overall_status}")
        
        print(f"\n📋 Test Results:")
        for test_name, test_data in self.test_results['tests'].items():
            status = test_data['status']
            emoji = {'PASS': '✅', 'WARNING': '⚠️', 'FAIL': '❌', 'RUNNING': '🔄'}.get(status, '❓')
            print(f"   {emoji} {test_name.replace('_', ' ').title()}: {status}")
        
        if self.test_results['critical_failures']:
            print(f"\n❌ Critical Failures ({len(self.test_results['critical_failures'])}):")
            for failure in self.test_results['critical_failures']:
                print(f"   - {failure}")
        
        if self.test_results['warnings']:
            print(f"\n⚠️ Warnings ({len(self.test_results['warnings'])}):")
            for warning in self.test_results['warnings']:
                print(f"   - {warning}")
        
        print(f"\n🕒 Test completed at: {self.test_results['timestamp']}")


async def run_system_health_check():
    """Run the complete system health check."""
    checker = SystemHealthChecker()
    results = await checker.run_all_health_checks()
    checker.print_summary()
    
    # Save results to file
    results_file = Path("tests/integration_results/system_health_results.json")
    results_file.parent.mkdir(exist_ok=True)
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_system_health_check())
