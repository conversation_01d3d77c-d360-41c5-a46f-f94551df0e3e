#!/usr/bin/env python3
"""
Fix the Self-Healer synchronization issue by ensuring the dashboard connects to the 
running healer manager instance and processes detected errors.
"""

import asyncio
import sys
import json
import requests
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))


async def fix_healer_synchronization():
    """Fix the healer manager synchronization issue."""
    print("=" * 80)
    print("FIXING SELF-HEALER SYNCHRONIZATION ISSUE")
    print("=" * 80)
    
    # Step 1: Check if N8N Builder with Self-Healer is running
    print("Step 1: Checking N8N Builder + Self-Healer status...")
    
    try:
        # Check N8N Builder
        n8n_response = requests.get("http://localhost:8002/", timeout=5)
        print(f"✅ N8N Builder running (status: {n8n_response.status_code})")
        
        # Check Dashboard
        dashboard_response = requests.get("http://localhost:8081/api/status", timeout=5)
        dashboard_data = dashboard_response.json()
        print(f"✅ Dashboard accessible (status: {dashboard_response.status_code})")
        print(f"   Dashboard shows: status={dashboard_data.get('status')}, is_running={dashboard_data.get('is_running')}")
        print(f"   Errors detected: {dashboard_data.get('metrics', {}).get('total_errors_detected', 0)}")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Service check failed: {e}")
        print("Please ensure N8N Builder is running with: python run.py")
        return
    
    # Step 2: Force the running healer manager to rescan and process errors
    print("\nStep 2: Forcing healer manager to rescan and process errors...")
    
    # We need to trigger the healer manager to rescan logs
    # This can be done by making a request to trigger error processing
    
    try:
        # Generate a new error to trigger the system
        print("Generating new error to trigger healer manager...")
        error_request = {
            "description": "Test error for healer synchronization - this should trigger error detection and processing"
        }
        
        test_response = requests.post(
            "http://localhost:8002/generate",
            headers={"Content-Type": "application/json"},
            json=error_request,
            timeout=30
        )
        print(f"✅ Test request completed (status: {test_response.status_code})")
        
        # Wait for error processing
        print("Waiting 10 seconds for error processing...")
        await asyncio.sleep(10)
        
        # Check dashboard again
        dashboard_response = requests.get("http://localhost:8081/api/status", timeout=5)
        updated_data = dashboard_response.json()
        
        print(f"\nUpdated dashboard status:")
        print(f"   Status: {updated_data.get('status')}")
        print(f"   Is running: {updated_data.get('is_running')}")
        print(f"   Errors detected: {updated_data.get('metrics', {}).get('total_errors_detected', 0)}")
        print(f"   Healing attempts: {updated_data.get('metrics', {}).get('total_healing_attempts', 0)}")
        
        if updated_data.get('metrics', {}).get('total_errors_detected', 0) > 0:
            print("✅ Healer manager is now detecting errors!")
        else:
            print("⚠️  Healer manager still shows 0 errors - deeper investigation needed")
            
    except Exception as e:
        print(f"❌ Error during synchronization test: {e}")
    
    # Step 3: Check for missing API endpoints
    print("\nStep 3: Checking dashboard API endpoints...")
    
    endpoints = ['/api/status', '/api/metrics', '/api/sessions', '/api/learning']
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:8081{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint}: OK")
            else:
                print(f"❌ {endpoint}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
    
    # Step 4: Provide recommendations
    print("\n" + "=" * 80)
    print("RECOMMENDATIONS")
    print("=" * 80)
    
    if updated_data.get('metrics', {}).get('total_errors_detected', 0) == 0:
        print("🔧 CRITICAL ISSUE: Healer manager not processing errors")
        print("   Possible causes:")
        print("   1. Dashboard connected to different healer manager instance")
        print("   2. Error monitor not sharing data with healer manager")
        print("   3. Healer manager monitoring loop not running")
        print()
        print("   Solutions:")
        print("   1. Restart N8N Builder to ensure single healer manager instance")
        print("   2. Check healer manager monitoring loop is active")
        print("   3. Verify error monitor and healer manager are properly connected")
    
    if dashboard_response.status_code != 200 or any(endpoint for endpoint in ['/api/metrics', '/api/sessions', '/api/learning'] if requests.get(f"http://localhost:8081{endpoint}", timeout=5).status_code != 200):
        print("🔧 DASHBOARD ISSUE: Missing API endpoints")
        print("   Solutions:")
        print("   1. Check dashboard route registration")
        print("   2. Verify all dashboard endpoints are properly defined")
        print("   3. Check for dashboard startup errors")
    
    print("\n🎯 NEXT STEPS:")
    print("1. If healer manager shows 0 errors: Restart N8N Builder")
    print("2. If KnowledgeBase errors: Run database schema setup")
    print("3. If dashboard endpoints missing: Check dashboard initialization")
    print("4. Monitor dashboard for real-time updates after fixes")


if __name__ == "__main__":
    asyncio.run(fix_healer_synchronization())
