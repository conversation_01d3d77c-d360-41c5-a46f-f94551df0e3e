#!/usr/bin/env python3
"""
Debug script to check Self-Healer error detection status.
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from Self_Healer.core.error_monitor import ErrorMonitor
from Self_Healer.core.healer_manager import SelfHealerManager


async def debug_error_detection():
    """Debug the error detection system."""
    print("=== Self-Healer Error Detection Debug ===")
    print(f"Timestamp: {datetime.now()}")
    print()
    
    # Initialize error monitor
    log_directory = Path("logs")
    error_monitor = ErrorMonitor(log_directory)
    
    print(f"Log directory: {log_directory.absolute()}")
    print(f"Error log path: {error_monitor.error_log_path}")
    print(f"Main log path: {error_monitor.main_log_path}")
    print()
    
    # Check if log files exist
    print("=== Log File Status ===")
    print(f"Error log exists: {error_monitor.error_log_path.exists()}")
    print(f"Main log exists: {error_monitor.main_log_path.exists()}")
    
    if error_monitor.error_log_path.exists():
        print(f"Error log size: {error_monitor.error_log_path.stat().st_size} bytes")
        print(f"Error log modified: {datetime.fromtimestamp(error_monitor.error_log_path.stat().st_mtime)}")
    
    if error_monitor.main_log_path.exists():
        print(f"Main log size: {error_monitor.main_log_path.stat().st_size} bytes")
        print(f"Main log modified: {datetime.fromtimestamp(error_monitor.main_log_path.stat().st_mtime)}")
    print()
    
    # Start error monitor
    await error_monitor.start()
    
    # Check detected errors
    print("=== Detected Errors ===")
    print(f"Total detected errors: {len(error_monitor.detected_errors)}")
    
    if error_monitor.detected_errors:
        for error_id, detected_error in error_monitor.detected_errors.items():
            print(f"  Error ID: {error_id}")
            print(f"    Timestamp: {detected_error.timestamp}")
            print(f"    Severity: {detected_error.severity}")
            print(f"    Category: {detected_error.category}")
            print(f"    Frequency: {detected_error.frequency}")
            print(f"    Message: {detected_error.error_detail.message[:100]}...")
            print()
    else:
        print("  No errors detected")
    print()
    
    # Check for new errors
    print("=== New Errors Check ===")
    new_errors = await error_monitor.get_new_errors()
    print(f"New errors found: {len(new_errors)}")
    
    for i, error in enumerate(new_errors):
        print(f"  New Error {i+1}:")
        print(f"    Title: {error.title}")
        print(f"    Category: {error.category}")
        print(f"    Severity: {error.severity}")
        print(f"    Message: {error.message}")
        print()
    
    # Check error statistics
    print("=== Error Statistics ===")
    stats = error_monitor.get_error_statistics()
    print(json.dumps(stats, indent=2, default=str))
    print()
    
    # Check recent log entries manually
    print("=== Recent Error Log Entries ===")
    if error_monitor.error_log_path.exists():
        with open(error_monitor.error_log_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            recent_lines = lines[-10:]  # Last 10 lines
            
            print(f"Total lines in error log: {len(lines)}")
            print("Last 10 lines:")
            for i, line in enumerate(recent_lines, start=len(lines)-len(recent_lines)+1):
                print(f"  {i:3d}: {line.strip()}")
    print()
    
    # Check recent main log entries for validation errors
    print("=== Recent Main Log Validation Errors ===")
    if error_monitor.main_log_path.exists():
        with open(error_monitor.main_log_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            
            # Find validation error lines
            validation_lines = []
            for i, line in enumerate(lines):
                if 'validation' in line.lower() and ('error' in line.lower() or 'ERROR' in line):
                    validation_lines.append((i+1, line.strip()))
            
            print(f"Total validation error lines found: {len(validation_lines)}")
            if validation_lines:
                print("Recent validation errors:")
                for line_num, line in validation_lines[-5:]:  # Last 5 validation errors
                    print(f"  {line_num:3d}: {line}")
    print()
    
    # Test error pattern matching
    print("=== Error Pattern Testing ===")
    test_lines = [
        "[2025-06-28 01:42:50,968] ERROR n8n_builder.validation: Workflow validation error: Workflow should have at least one trigger node",
        "[2025-06-28 01:42:50,970] ERROR n8n_builder.n8n_builder: Error generating workflow: Generated workflow failed validation"
    ]

    test_timestamp = datetime.now()

    for line in test_lines:
        print(f"Testing line: {line}")
        try:
            error_detail = error_monitor._parse_error_line(line, "test.log", 1, test_timestamp)
            if error_detail:
                print(f"  ✓ Detected: {error_detail['pattern_name']} - {error_detail['error_message']}")
                print(f"    Severity: {error_detail['severity']}")
            else:
                print(f"  ✗ Not detected")
        except Exception as e:
            print(f"  ✗ Error parsing: {e}")
        print()
    
    await error_monitor.stop()


async def debug_healer_manager():
    """Debug the healer manager status."""
    print("=== Self-Healer Manager Debug ===")
    
    try:
        # Initialize healer manager
        log_directory = Path("logs")
        healer_manager = SelfHealerManager(log_directory)
        
        print(f"Healer manager initialized: {healer_manager is not None}")
        print(f"Is running: {healer_manager.is_running}")
        print(f"Status: {healer_manager.status}")
        print(f"Active sessions: {len(healer_manager.active_sessions)}")
        print(f"Session history: {len(healer_manager.session_history)}")
        print()
        
        # Get status
        if healer_manager.is_running:
            status = await healer_manager.get_status()
            print("=== Healer Manager Status ===")
            print(json.dumps(status, indent=2, default=str))
        else:
            print("Healer manager is not running")
        
    except Exception as e:
        print(f"Error debugging healer manager: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Main debug function."""
    print("Starting Self-Healer debug session...")
    print("=" * 60)
    
    await debug_error_detection()
    print("=" * 60)
    await debug_healer_manager()
    
    print("=" * 60)
    print("Debug session complete.")


if __name__ == "__main__":
    asyncio.run(main())
