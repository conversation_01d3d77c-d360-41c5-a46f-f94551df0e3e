#!/usr/bin/env python3
"""
Quick Self-Healer Investigation
==============================
Focused investigation to identify the exact disconnect issue.
"""

import asyncio
import sys
import requests
import json
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from Self_Healer.core.error_monitor import ErrorMonitor


async def quick_investigation():
    """Quick focused investigation."""
    print("=" * 60)
    print("QUICK SELF-HEALER INVESTIGATION")
    print("=" * 60)
    
    # Step 1: Check services
    print("1. SERVICE STATUS:")
    try:
        n8n_response = requests.get("http://localhost:8002/", timeout=5)
        print(f"   ✅ N8N Builder: UP ({n8n_response.status_code})")
    except Exception as e:
        print(f"   ❌ N8N Builder: DOWN - {e}")
        return
    
    try:
        dashboard_response = requests.get("http://localhost:8081/api/status", timeout=5)
        dashboard_data = dashboard_response.json()
        print(f"   ✅ Dashboard: UP ({dashboard_response.status_code})")
        print(f"      Dashboard errors: {dashboard_data.get('metrics', {}).get('total_errors_detected', 'Unknown')}")
    except Exception as e:
        print(f"   ❌ Dashboard: DOWN - {e}")
        return
    
    # Step 2: Check error monitor
    print("\n2. ERROR MONITOR TEST:")
    try:
        error_monitor = ErrorMonitor()
        await error_monitor.start()
        
        print(f"   Initial errors in memory: {len(error_monitor.detected_errors)}")
        
        # Force rescan
        await error_monitor.force_rescan_logs(hours_back=2)
        
        print(f"   Errors after rescan: {len(error_monitor.detected_errors)}")
        
        if error_monitor.detected_errors:
            print("   ✅ Error Monitor is detecting errors")
            # Show first few errors
            for i, (error_id, error) in enumerate(list(error_monitor.detected_errors.items())[:3]):
                print(f"      Error {i+1}: {error.get('message', 'No message')[:50]}...")
        else:
            print("   ⚠️  Error Monitor found no errors")
        
        await error_monitor.stop()
        
    except Exception as e:
        print(f"   ❌ Error Monitor failed: {e}")
        return
    
    # Step 3: Compare counts
    print("\n3. DISCONNECT ANALYSIS:")
    monitor_errors = len(error_monitor.detected_errors) if 'error_monitor' in locals() else 0
    dashboard_errors = dashboard_data.get('metrics', {}).get('total_errors_detected', 0)
    
    print(f"   Error Monitor detected: {monitor_errors}")
    print(f"   Dashboard shows: {dashboard_errors}")
    print(f"   Difference: {monitor_errors - dashboard_errors}")
    
    if monitor_errors > 0 and dashboard_errors == 0:
        print("   🚨 CRITICAL: Complete disconnect confirmed!")
        print("   The error monitor is working but dashboard shows 0 errors.")
        print("   This indicates the healer manager instances are not synchronized.")
    elif monitor_errors != dashboard_errors:
        print("   ⚠️  PARTIAL: Some errors not reaching dashboard")
    else:
        print("   ✅ SYNCHRONIZED: Counts match")
    
    # Step 4: Check for multiple instances
    print("\n4. INSTANCE CHECK:")
    try:
        # Check recent logs for multiple initializations
        log_file = Path("logs/n8n_builder.log")
        if log_file.exists():
            with open(log_file, 'r') as f:
                content = f.read()
            
            # Look for recent healer manager starts
            lines = content.split('\n')
            recent_starts = [line for line in lines[-200:] if "Self-Healer Manager" in line and ("initialized" in line or "started" in line)]
            
            print(f"   Recent healer manager events: {len(recent_starts)}")
            for event in recent_starts[-3:]:  # Show last 3
                print(f"      {event}")
                
            if len(recent_starts) > 2:
                print("   ⚠️  Multiple healer manager instances detected!")
            else:
                print("   ✅ Single instance detected")
    except Exception as e:
        print(f"   ❌ Instance check failed: {e}")
    
    print("\n" + "=" * 60)
    print("CONCLUSION:")
    if monitor_errors > 0 and dashboard_errors == 0:
        print("🎯 ROOT CAUSE: Dashboard connected to different healer manager instance")
        print("🔧 SOLUTION: Ensure single healer manager instance across system")
    else:
        print("🤔 Need deeper investigation - issue may be elsewhere")


if __name__ == "__main__":
    asyncio.run(quick_investigation())
