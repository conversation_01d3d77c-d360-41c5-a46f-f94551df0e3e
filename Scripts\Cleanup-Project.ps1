# Cleanup-Project.ps1
# PowerShell script for N8N Builder project cleanup to reduce file count

param(
    [switch]$DryRun = $true,
    [switch]$Execute,
    [string[]]$Operations = @('cache', 'duplicates', 'obsolete', 'logs'),
    [int]$MaxObsolete = 100,
    [switch]$Analyze,
    [switch]$Report,
    [switch]$Help
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$VenvPath = Join-Path $ProjectRoot "venv"
$PythonExe = Join-Path $VenvPath "Scripts\python.exe"
$CleanupScript = Join-Path $ScriptDir "project_cleanup_manager.py"
$AnalysisScript = Join-Path $ScriptDir "analyze_project_files.py"

function Show-Help {
    Write-Host "🧹 N8N Builder Project Cleanup Manager" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "PURPOSE:" -ForegroundColor Yellow
    Write-Host "  Reduce project file count from 5000+ to manageable levels"
    Write-Host "  Safely archive duplicates, obsolete files, and cache data"
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor Yellow
    Write-Host "  .\Cleanup-Project.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor Yellow
    Write-Host "  -DryRun         Preview cleanup operations (default)"
    Write-Host "  -Execute        Actually perform cleanup operations"
    Write-Host "  -Operations     Specify operations: cache, duplicates, obsolete, logs"
    Write-Host "  -MaxObsolete    Maximum obsolete files to process (default: 100)"
    Write-Host "  -Analyze        Run project analysis first"
    Write-Host "  -Report         Show cleanup report"
    Write-Host "  -Help           Show this help message"
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Yellow
    Write-Host "  .\Cleanup-Project.ps1 -DryRun"
    Write-Host "  .\Cleanup-Project.ps1 -Execute -Operations cache,duplicates"
    Write-Host "  .\Cleanup-Project.ps1 -Analyze"
    Write-Host "  .\Cleanup-Project.ps1 -Report"
    Write-Host ""
    Write-Host "SAFETY FEATURES:" -ForegroundColor Green
    Write-Host "  ✅ Dry run by default"
    Write-Host "  ✅ Files archived, not deleted"
    Write-Host "  ✅ Maintains project structure"
    Write-Host "  ✅ Detailed logging and reporting"
    Write-Host "  ✅ Batch size limits for safety"
}

function Test-Prerequisites {
    Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow
    
    # Check if virtual environment exists
    if (-not (Test-Path $VenvPath)) {
        Write-Host "❌ Virtual environment not found at: $VenvPath" -ForegroundColor Red
        Write-Host "   Please run setup.py or create virtual environment first" -ForegroundColor Red
        return $false
    }
    
    # Check if Python executable exists
    if (-not (Test-Path $PythonExe)) {
        Write-Host "❌ Python executable not found at: $PythonExe" -ForegroundColor Red
        return $false
    }
    
    # Check if cleanup script exists
    if (-not (Test-Path $CleanupScript)) {
        Write-Host "❌ Cleanup script not found at: $CleanupScript" -ForegroundColor Red
        return $false
    }
    
    Write-Host "✅ Prerequisites check passed" -ForegroundColor Green
    return $true
}

function Show-ProjectStatus {
    Write-Host "📊 Current Project Status" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    
    # Count total files
    $TotalFiles = (Get-ChildItem -Path $ProjectRoot -Recurse -File | Measure-Object).Count
    Write-Host "📁 Total files: $TotalFiles" -ForegroundColor White
    
    # Check for analysis report
    $AnalysisReport = Join-Path $ScriptDir "project_analysis_report.json"
    if (Test-Path $AnalysisReport) {
        try {
            $Analysis = Get-Content $AnalysisReport | ConvertFrom-Json
            $Summary = $Analysis.summary
            
            Write-Host "📋 Analysis Summary:" -ForegroundColor Yellow
            Write-Host "   Total files: $($Summary.total_files)" -ForegroundColor White
            Write-Host "   Duplicate groups: $($Summary.duplicate_groups)" -ForegroundColor White
            Write-Host "   Obsolete files: $($Summary.obsolete_files)" -ForegroundColor White
            Write-Host "   Total size: $([math]::Round($Summary.total_size / 1MB, 2)) MB" -ForegroundColor White
            Write-Host "   Analysis date: $($Analysis.analysis_date)" -ForegroundColor Gray
        }
        catch {
            Write-Host "⚠️ Could not parse analysis report" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ No analysis report found - run with -Analyze first" -ForegroundColor Yellow
    }
    
    # Check archive directories
    $ArchiveDir = Join-Path $ProjectRoot "Archive"
    if (Test-Path $ArchiveDir) {
        $ArchivedFiles = (Get-ChildItem -Path $ArchiveDir -Recurse -File | Measure-Object).Count
        Write-Host "📦 Archived files: $ArchivedFiles" -ForegroundColor Green
    } else {
        Write-Host "📦 No archive directory found" -ForegroundColor Gray
    }
    
    # Check for large directories
    Write-Host "📂 Largest directories:" -ForegroundColor Yellow
    $LargeDirs = Get-ChildItem -Path $ProjectRoot -Directory | ForEach-Object {
        $Size = (Get-ChildItem -Path $_.FullName -Recurse -File | Measure-Object -Property Length -Sum).Sum
        [PSCustomObject]@{
            Name = $_.Name
            SizeMB = [math]::Round($Size / 1MB, 2)
            FileCount = (Get-ChildItem -Path $_.FullName -Recurse -File | Measure-Object).Count
        }
    } | Sort-Object SizeMB -Descending | Select-Object -First 5
    
    foreach ($dir in $LargeDirs) {
        Write-Host "   $($dir.Name): $($dir.SizeMB) MB ($($dir.FileCount) files)" -ForegroundColor White
    }
}

function Invoke-ProjectAnalysis {
    Write-Host "🔍 Running project analysis..." -ForegroundColor Yellow
    
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    try {
        & $PythonExe $AnalysisScript
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Project analysis completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Project analysis failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error during analysis: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Invoke-ProjectCleanup {
    param(
        [bool]$ExecuteCleanup,
        [string[]]$CleanupOperations,
        [int]$MaxObsoleteFiles
    )
    
    $Mode = if ($ExecuteCleanup) { "EXECUTE" } else { "DRY RUN" }
    Write-Host "🧹 Running project cleanup ($Mode)..." -ForegroundColor Yellow
    
    if (-not (Test-Prerequisites)) {
        return $false
    }
    
    # Build command arguments
    $Args = @($CleanupScript)
    
    if ($ExecuteCleanup) {
        $Args += "--execute"
    }
    
    if ($CleanupOperations.Count -gt 0) {
        $Args += "--operations"
        $Args += $CleanupOperations
    }
    
    if ($MaxObsoleteFiles -gt 0) {
        $Args += "--max-obsolete"
        $Args += $MaxObsoleteFiles.ToString()
    }
    
    try {
        Write-Host "Command: python $($Args -join ' ')" -ForegroundColor Gray
        & $PythonExe @Args
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Project cleanup completed" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Project cleanup failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error during cleanup: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-CleanupReport {
    Write-Host "📊 Cleanup Report" -ForegroundColor Cyan
    Write-Host "=================" -ForegroundColor Cyan
    
    $ReportFile = Join-Path $ProjectRoot "logs\project_cleanup_report.json"
    
    if (Test-Path $ReportFile) {
        try {
            $Report = Get-Content $ReportFile | ConvertFrom-Json
            $Summary = $Report.summary
            
            Write-Host "📋 Cleanup Summary:" -ForegroundColor Yellow
            Write-Host "   Files moved: $($Summary.files_moved)" -ForegroundColor White
            Write-Host "   Files deleted: $($Summary.files_deleted)" -ForegroundColor White
            Write-Host "   Space saved: $($Summary.space_saved_mb) MB" -ForegroundColor White
            Write-Host "   Dry run: $($Report.dry_run)" -ForegroundColor White
            Write-Host "   Date: $($Report.cleanup_date)" -ForegroundColor Gray
            
            if ($Report.operations -and $Report.operations.Count -gt 0) {
                Write-Host "📝 Operations performed:" -ForegroundColor Yellow
                foreach ($op in $Report.operations) {
                    Write-Host "   - $op" -ForegroundColor White
                }
            }
        }
        catch {
            Write-Host "⚠️ Could not parse cleanup report" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ No cleanup report found" -ForegroundColor Yellow
        Write-Host "   Run cleanup operations first to generate a report" -ForegroundColor Gray
    }
}

function Show-RecommendedActions {
    Write-Host "💡 Recommended Actions" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    
    # Check if analysis exists
    $AnalysisReport = Join-Path $ScriptDir "project_analysis_report.json"
    if (-not (Test-Path $AnalysisReport)) {
        Write-Host "1. Run analysis first:" -ForegroundColor Yellow
        Write-Host "   .\Cleanup-Project.ps1 -Analyze" -ForegroundColor White
        return
    }
    
    try {
        $Analysis = Get-Content $AnalysisReport | ConvertFrom-Json
        $Summary = $Analysis.summary
        
        Write-Host "Based on analysis results:" -ForegroundColor Yellow
        
        if ($Summary.duplicate_groups -gt 100) {
            Write-Host "1. Archive duplicate files (high priority):" -ForegroundColor Yellow
            Write-Host "   .\Cleanup-Project.ps1 -Execute -Operations duplicates" -ForegroundColor White
        }
        
        if ($Summary.obsolete_files -gt 500) {
            Write-Host "2. Archive obsolete files (medium priority):" -ForegroundColor Yellow
            Write-Host "   .\Cleanup-Project.ps1 -Execute -Operations obsolete -MaxObsolete 200" -ForegroundColor White
        }
        
        Write-Host "3. Clean cache directories (safe):" -ForegroundColor Yellow
        Write-Host "   .\Cleanup-Project.ps1 -Execute -Operations cache" -ForegroundColor White
        
        Write-Host "4. Archive large log files:" -ForegroundColor Yellow
        Write-Host "   .\Cleanup-Project.ps1 -Execute -Operations logs" -ForegroundColor White
        
        Write-Host "5. Full cleanup (when ready):" -ForegroundColor Yellow
        Write-Host "   .\Cleanup-Project.ps1 -Execute" -ForegroundColor White
        
    }
    catch {
        Write-Host "⚠️ Could not parse analysis for recommendations" -ForegroundColor Yellow
    }
}

# Main execution logic
function Main {
    Write-Host "🧹 N8N Builder Project Cleanup Manager" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    Write-Host ""
    
    # Show help if requested
    if ($Help) {
        Show-Help
        return
    }
    
    # Override DryRun if Execute is specified
    if ($Execute) {
        $DryRun = $false
    }
    
    # Execute based on parameters
    if ($Analyze) {
        if (Invoke-ProjectAnalysis) {
            Write-Host ""
            Show-ProjectStatus
        }
    }
    elseif ($Report) {
        Show-CleanupReport
    }
    else {
        # Show current status
        Show-ProjectStatus
        Write-Host ""
        
        # Run cleanup
        if (Invoke-ProjectCleanup -ExecuteCleanup (-not $DryRun) -CleanupOperations $Operations -MaxObsoleteFiles $MaxObsolete) {
            Write-Host ""
            Show-CleanupReport
        }
        
        Write-Host ""
        Show-RecommendedActions
    }
}

# Run the main function
Main
