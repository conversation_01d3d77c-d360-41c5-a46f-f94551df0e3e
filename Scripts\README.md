# N8N_Builder Scripts

This directory contains utility scripts and tools for managing N8N_Builder.

## 📁 Script Categories

### 🔧 Project Management
- **project_cleanup_manager.py**: Clean up temporary files and optimize project structure
- **analyze_project_files.py**: Analyze project structure and generate reports
- **safe_cleanup.py**: Safely remove unnecessary files while preserving important data

### 📊 Analysis Tools
- **generate_process_flow.py**: Generate process flow documentation
- **validate_stored_procedures.py**: Validate database stored procedures

### 🔄 Log Management
- **setup_log_rotation.py**: Configure automatic log rotation
- **Setup-LogRotation.ps1**: PowerShell script for Windows log rotation setup

### 🧪 Testing Utilities
- **test_logging.py**: Test logging configuration and functionality
- **test_safe_cleanup.py**: Test cleanup operations safely

## 🚀 Usage

### Running Python Scripts
```bash
# From the project root directory
python Scripts/script_name.py
```

### Running PowerShell Scripts
```powershell
# From the project root directory
.\Scripts\Script-Name.ps1
```

## ⚙️ Configuration

Most scripts use configuration from:
- **Project root config files**: `config_public.yaml`, etc.
- **Environment variables**: Set in `.env` files
- **Command line arguments**: Check individual script help

## 🛡️ Safety Features

- **Backup creation**: Scripts create backups before making changes
- **Dry-run modes**: Test operations without making actual changes
- **Validation checks**: Verify operations before execution
- **Rollback capabilities**: Undo changes if needed

## 📋 Best Practices

1. **Always backup** important data before running cleanup scripts
2. **Test in development** before running in production
3. **Review logs** after script execution
4. **Use dry-run modes** when available

## 🔗 Integration

These scripts integrate with:
- **N8N_Builder core**: Main application functionality
- **Database systems**: SQL Server, PostgreSQL
- **Docker environments**: Container management
- **CI/CD pipelines**: Automated deployment processes
