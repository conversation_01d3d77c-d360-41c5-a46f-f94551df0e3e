#!/usr/bin/env python3
"""
Comprehensive test of the N8N Builder --> Error --> Self-Healer --> KnowledgeBase --> Dashboard pipeline.
This script will trace each step and identify where the workflow breaks down.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import requests

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from Self_Healer.core.error_monitor import ErrorMonitor


class PipelineTestSuite:
    """Test suite for the complete healing pipeline."""
    
    def __init__(self):
        self.results = {}
        self.log_directory = Path("logs")
        self.healer_manager = None
        self.error_monitor = None
        
    async def run_complete_test(self):
        """Run the complete pipeline test."""
        print("=" * 80)
        print("HEALING PIPELINE DIAGNOSTIC TEST")
        print("=" * 80)
        print(f"Test started at: {datetime.now()}")
        print()
        
        # Step 1: Test N8N Builder Error Generation
        await self.test_step_1_n8n_builder_error()
        
        # Step 2: Test Error Detection
        await self.test_step_2_error_detection()
        
        # Step 3: Test Self-Healer Processing
        await self.test_step_3_self_healer_processing()
        
        # Step 4: Test KnowledgeBase Integration
        await self.test_step_4_knowledgebase_integration()
        
        # Step 5: Test Dashboard Data Flow
        await self.test_step_5_dashboard_data_flow()
        
        # Generate final report
        self.generate_final_report()
    
    async def test_step_1_n8n_builder_error(self):
        """Step 1: Generate an error in N8N Builder and verify it's logged."""
        print("STEP 1: Testing N8N Builder Error Generation")
        print("-" * 50)
        
        try:
            # Check if N8N Builder is running
            try:
                response = requests.get("http://localhost:8002/", timeout=5)
                print(f"✅ N8N Builder is running (status: {response.status_code})")
            except requests.exceptions.RequestException as e:
                print(f"❌ N8N Builder is not running: {e}")
                self.results['step_1'] = {'status': 'FAILED', 'reason': 'N8N Builder not running'}
                return
            
            # Record current log file sizes
            error_log_path = self.log_directory / "errors.log"
            main_log_path = self.log_directory / "n8n_builder.log"
            
            initial_error_size = error_log_path.stat().st_size if error_log_path.exists() else 0
            initial_main_size = main_log_path.stat().st_size if main_log_path.exists() else 0
            
            print(f"Initial error log size: {initial_error_size} bytes")
            print(f"Initial main log size: {initial_main_size} bytes")
            
            # Generate an error by making a request that should fail
            print("Generating error by requesting invalid workflow...")
            
            error_request = {
                "description": "Create a workflow that will definitely fail validation with no trigger nodes and invalid structure"
            }
            
            try:
                response = requests.post(
                    "http://localhost:8002/generate",
                    headers={"Content-Type": "application/json"},
                    json=error_request,
                    timeout=30
                )
                print(f"Request completed with status: {response.status_code}")
                
                # Wait a moment for logs to be written
                await asyncio.sleep(2)
                
                # Check if new errors were logged
                final_error_size = error_log_path.stat().st_size if error_log_path.exists() else 0
                final_main_size = main_log_path.stat().st_size if main_log_path.exists() else 0
                
                error_log_growth = final_error_size - initial_error_size
                main_log_growth = final_main_size - initial_main_size
                
                print(f"Error log growth: {error_log_growth} bytes")
                print(f"Main log growth: {main_log_growth} bytes")
                
                if error_log_growth > 0 or main_log_growth > 0:
                    print("✅ New log entries detected")
                    
                    # Show recent log entries
                    if error_log_growth > 0:
                        print("\nRecent error log entries:")
                        with open(error_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                            for line in lines[-3:]:  # Last 3 lines
                                print(f"  {line.strip()}")
                    
                    if main_log_growth > 0:
                        print("\nRecent main log entries:")
                        with open(main_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()
                            for line in lines[-3:]:  # Last 3 lines
                                if 'ERROR' in line or 'error' in line.lower():
                                    print(f"  {line.strip()}")
                    
                    self.results['step_1'] = {
                        'status': 'SUCCESS',
                        'error_log_growth': error_log_growth,
                        'main_log_growth': main_log_growth
                    }
                else:
                    print("⚠️  No new log entries detected")
                    self.results['step_1'] = {'status': 'WARNING', 'reason': 'No log growth detected'}
                
            except requests.exceptions.RequestException as e:
                print(f"❌ Request failed: {e}")
                self.results['step_1'] = {'status': 'FAILED', 'reason': f'Request failed: {e}'}
                
        except Exception as e:
            print(f"❌ Step 1 failed: {e}")
            self.results['step_1'] = {'status': 'FAILED', 'reason': str(e)}
        
        print()
    
    async def test_step_2_error_detection(self):
        """Step 2: Test if Self-Healer error monitor detects the errors."""
        print("STEP 2: Testing Self-Healer Error Detection")
        print("-" * 50)
        
        try:
            # Initialize error monitor
            self.error_monitor = ErrorMonitor(self.log_directory)
            await self.error_monitor.start()
            
            print("✅ Error monitor started")
            
            # Check current detected errors
            initial_errors = len(self.error_monitor.detected_errors)
            print(f"Initial detected errors: {initial_errors}")
            
            # Force a rescan to pick up recent errors
            print("Forcing rescan of logs...")
            new_errors_found = await self.error_monitor.force_rescan_logs(hours_back=1)
            
            final_errors = len(self.error_monitor.detected_errors)
            print(f"Errors after rescan: {final_errors}")
            print(f"New errors found: {new_errors_found}")
            
            if new_errors_found > 0:
                print("✅ Error detection working")
                
                # Show detected errors
                print("\nDetected errors:")
                for error_id, detected_error in list(self.error_monitor.detected_errors.items())[-3:]:
                    print(f"  ID: {error_id}")
                    print(f"    Timestamp: {detected_error.timestamp}")
                    print(f"    Severity: {detected_error.severity}")
                    print(f"    Category: {detected_error.category}")
                    print(f"    Message: {detected_error.error_detail.message}")
                    print()
                
                self.results['step_2'] = {
                    'status': 'SUCCESS',
                    'new_errors_found': new_errors_found,
                    'total_errors': final_errors
                }
            else:
                print("⚠️  No new errors detected by error monitor")
                self.results['step_2'] = {'status': 'WARNING', 'reason': 'No new errors detected'}
            
            await self.error_monitor.stop()
            
        except Exception as e:
            print(f"❌ Step 2 failed: {e}")
            self.results['step_2'] = {'status': 'FAILED', 'reason': str(e)}
        
        print()
    
    async def test_step_3_self_healer_processing(self):
        """Step 3: Test if Self-Healer manager processes the errors."""
        print("STEP 3: Testing Self-Healer Manager Processing")
        print("-" * 50)
        
        try:
            # Check if healer manager is running by connecting to it
            # We'll test the API endpoints that the dashboard uses
            
            try:
                response = requests.get("http://localhost:8081/api/status", timeout=5)
                if response.status_code == 200:
                    status_data = response.json()
                    print("✅ Self-Healer dashboard API accessible")
                    print(f"Status: {status_data.get('status', 'unknown')}")
                    print(f"Is running: {status_data.get('is_running', False)}")
                    print(f"Active sessions: {status_data.get('active_sessions', 0)}")
                    
                    metrics = status_data.get('metrics', {})
                    print(f"Total errors detected: {metrics.get('total_errors_detected', 0)}")
                    print(f"Total healing attempts: {metrics.get('total_healing_attempts', 0)}")
                    
                    self.results['step_3'] = {
                        'status': 'SUCCESS',
                        'healer_status': status_data.get('status'),
                        'is_running': status_data.get('is_running'),
                        'metrics': metrics
                    }
                else:
                    print(f"⚠️  Dashboard API returned status {response.status_code}")
                    self.results['step_3'] = {'status': 'WARNING', 'reason': f'API status {response.status_code}'}
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ Cannot connect to Self-Healer dashboard: {e}")
                self.results['step_3'] = {'status': 'FAILED', 'reason': f'Dashboard not accessible: {e}'}
                
        except Exception as e:
            print(f"❌ Step 3 failed: {e}")
            self.results['step_3'] = {'status': 'FAILED', 'reason': str(e)}
        
        print()
    
    async def test_step_4_knowledgebase_integration(self):
        """Step 4: Test KnowledgeBase integration."""
        print("STEP 4: Testing KnowledgeBase Integration")
        print("-" * 50)
        
        try:
            # Test KnowledgeBase connection
            from Self_Healer.core.knowledge_integration import get_knowledge_integrator
            
            knowledge_integrator = get_knowledge_integrator()
            print("✅ KnowledgeBase integrator initialized")
            
            # Test database connection
            try:
                # Try a simple query to test connection
                test_query = "SELECT COUNT(*) as count FROM REF_Fact"
                result = await knowledge_integrator.db_tool.execute_query(test_query)
                
                if result and 'rows' in result:
                    fact_count = result['rows'][0]['count'] if result['rows'] else 0
                    print(f"✅ KnowledgeBase connection working - {fact_count} facts in database")
                    
                    self.results['step_4'] = {
                        'status': 'SUCCESS',
                        'fact_count': fact_count
                    }
                else:
                    print("⚠️  KnowledgeBase query returned unexpected result")
                    self.results['step_4'] = {'status': 'WARNING', 'reason': 'Unexpected query result'}
                    
            except Exception as e:
                print(f"❌ KnowledgeBase connection failed: {e}")
                self.results['step_4'] = {'status': 'FAILED', 'reason': f'Database connection failed: {e}'}
                
        except Exception as e:
            print(f"❌ Step 4 failed: {e}")
            self.results['step_4'] = {'status': 'FAILED', 'reason': str(e)}
        
        print()
    
    async def test_step_5_dashboard_data_flow(self):
        """Step 5: Test dashboard data flow and real-time updates."""
        print("STEP 5: Testing Dashboard Data Flow")
        print("-" * 50)
        
        try:
            # Test all dashboard API endpoints
            endpoints = [
                '/api/status',
                '/api/metrics', 
                '/api/sessions',
                '/api/learning'
            ]
            
            dashboard_data = {}
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f"http://localhost:8081{endpoint}", timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        dashboard_data[endpoint] = data
                        print(f"✅ {endpoint}: OK")
                    else:
                        print(f"⚠️  {endpoint}: Status {response.status_code}")
                        dashboard_data[endpoint] = {'error': f'Status {response.status_code}'}
                        
                except requests.exceptions.RequestException as e:
                    print(f"❌ {endpoint}: {e}")
                    dashboard_data[endpoint] = {'error': str(e)}
            
            # Analyze the data
            status_data = dashboard_data.get('/api/status', {})
            if 'error' not in status_data:
                print(f"\nDashboard Status Analysis:")
                print(f"  System Status: {status_data.get('status', 'unknown')}")
                print(f"  Running: {status_data.get('is_running', False)}")
                
                metrics = status_data.get('metrics', {})
                print(f"  Total Errors: {metrics.get('total_errors_detected', 0)}")
                print(f"  Healing Attempts: {metrics.get('total_healing_attempts', 0)}")
                print(f"  Success Rate: {metrics.get('success_rate', 0)}%")
                
                recent_sessions = status_data.get('recent_sessions', [])
                print(f"  Recent Sessions: {len(recent_sessions)}")
                
                self.results['step_5'] = {
                    'status': 'SUCCESS',
                    'dashboard_data': dashboard_data
                }
            else:
                print("⚠️  Dashboard status endpoint failed")
                self.results['step_5'] = {'status': 'WARNING', 'reason': 'Status endpoint failed'}
                
        except Exception as e:
            print(f"❌ Step 5 failed: {e}")
            self.results['step_5'] = {'status': 'FAILED', 'reason': str(e)}
        
        print()
    
    def generate_final_report(self):
        """Generate a final diagnostic report."""
        print("=" * 80)
        print("FINAL DIAGNOSTIC REPORT")
        print("=" * 80)
        
        total_steps = len(self.results)
        successful_steps = sum(1 for result in self.results.values() if result['status'] == 'SUCCESS')
        
        print(f"Overall Status: {successful_steps}/{total_steps} steps successful")
        print()
        
        for step, result in self.results.items():
            status = result['status']
            icon = "✅" if status == 'SUCCESS' else "⚠️" if status == 'WARNING' else "❌"
            print(f"{icon} {step.upper()}: {status}")
            
            if 'reason' in result:
                print(f"    Reason: {result['reason']}")
            
            # Show key metrics for successful steps
            if status == 'SUCCESS':
                if step == 'step_1' and 'error_log_growth' in result:
                    print(f"    Log growth: {result['error_log_growth']} bytes")
                elif step == 'step_2' and 'new_errors_found' in result:
                    print(f"    New errors found: {result['new_errors_found']}")
                elif step == 'step_3' and 'metrics' in result:
                    metrics = result['metrics']
                    print(f"    Errors detected: {metrics.get('total_errors_detected', 0)}")
                elif step == 'step_4' and 'fact_count' in result:
                    print(f"    Facts in KB: {result['fact_count']}")
        
        print()
        print("RECOMMENDATIONS:")
        print("-" * 40)
        
        # Generate specific recommendations based on failures
        if self.results.get('step_1', {}).get('status') != 'SUCCESS':
            print("• Check N8N Builder is running and accessible")
            print("• Verify logging configuration is working")
        
        if self.results.get('step_2', {}).get('status') != 'SUCCESS':
            print("• Check Self-Healer error monitor configuration")
            print("• Verify log file permissions and paths")
        
        if self.results.get('step_3', {}).get('status') != 'SUCCESS':
            print("• Check Self-Healer manager is running")
            print("• Verify dashboard is accessible on port 8081")
        
        if self.results.get('step_4', {}).get('status') != 'SUCCESS':
            print("• Check KnowledgeBase database connection")
            print("• Verify MCP Database Tool configuration")
        
        if self.results.get('step_5', {}).get('status') != 'SUCCESS':
            print("• Check dashboard API endpoints")
            print("• Verify real-time data flow")
        
        print()
        print(f"Test completed at: {datetime.now()}")


async def main():
    """Main test function."""
    test_suite = PipelineTestSuite()
    await test_suite.run_complete_test()


if __name__ == "__main__":
    asyncio.run(main())
