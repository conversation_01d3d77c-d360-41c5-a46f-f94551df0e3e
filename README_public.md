# N8N_Builder Community Edition: AI-Powered Workflow Automation

🤖 **Transform plain English into powerful N8N workflows using AI**

## 🚀 Quick Start (Choose Your Speed)

| Time Available | Start Here | What You'll Get |
|----------------|------------|-----------------|
| **2 minutes** | [⚡ Lightning Start](#lightning-start) | Working system, no explanations |
| **15 minutes** | [📖 Getting Started](#getting-started) | Understanding + customization |
| **30 minutes** | [🎯 First Workflow](#first-workflow) | Complete workflow tutorial |

## 🏗️ How It Works

```mermaid
graph LR
    A[Describe in English] --> B[AI Generates JSON]
    B --> C[Import to n8n]
    C --> D[Workflow Runs]

    classDef process fill:#e8f5e8
    class A,B,C,D process
```

**System Components:**
1. **🤖 N8N_Builder** - AI workflow generator with MCP Research integration
2. **🐳 n8n-docker** - Production execution environment
3. **🔄 Integration** - Seamless workflow transfer

## ✨ What You Can Build

**💡 Example Automations:**
- *"Send me an email when a new file is uploaded to my folder"*
- *"Post to Twitter when I publish a new blog article"*
- *"Convert CSV files to JSON and send to a webhook"*
- *"Alert me when my website goes down"*
- *"Send welcome emails to new customers"*

## 🎯 Key Features

- **🤖 AI-Powered**: Convert plain English to n8n workflows
- **🔍 Smart Research**: Real-time n8n documentation lookup via MCP Research Tool
- **⚡ Dual APIs**: Standard REST + AG-UI Protocol
- **✅ Validation**: Ensures workflows meet n8n standards
- **🔄 Iteration**: Modify existing workflows easily
- **🌐 Web Interface**: User-friendly workflow generation
- **🏭 Production Ready**: Complete Docker execution environment
- **📋 Comprehensive Testing**: System health monitoring and validation suite
- **🧹 Smart Automation**: PowerShell scripts for environment management
- **🛡️ Auto-Recovery**: Intelligent system startup and port management

## ⚡ Lightning Start

```bash
# 1. Clone and setup
git clone https://github.com/vbwyrde/N8N_Builder.git
cd N8N_Builder
python -m venv venv
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 2. Install dependencies
pip install -r requirements_public.txt

# 3. Start the system
python run_public.py

# 4. Open browser
# http://localhost:8002
```

## 📖 Getting Started

### **Prerequisites**
- Python 3.8+
- Virtual environment (recommended)
- Local AI model (LM Studio recommended) at localhost:1234/v1

### **Installation**

1. **Clone the repository**
```bash
git clone https://github.com/vbwyrde/N8N_Builder.git
cd N8N_Builder
```

2. **Create virtual environment**
```bash
python -m venv venv
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac
```

3. **Install dependencies**
```bash
pip install -r requirements_public.txt
```

4. **Configure environment**
```bash
# Copy environment template
cp .env.template .env
# Edit .env with your settings
```

5. **Start the system**
```bash
python run_public.py
```

## 🎯 First Workflow

1. **Open the web interface**: http://localhost:8002
2. **Describe your workflow**: "Send me an email when a CSV file is uploaded"
3. **Review generated workflow**: AI creates complete n8n JSON
4. **Import to n8n**: Copy JSON to your n8n instance
5. **Test and deploy**: Activate your workflow

## 🔧 System Architecture

### **Core Components**
- **FastAPI Server**: REST API for workflow generation
- **AG-UI Protocol**: Advanced agent interactions
- **MCP Research Tool**: Real-time n8n documentation lookup
- **Workflow Validator**: Ensures n8n compatibility
- **Knowledge Cache**: Optimized response caching

### **API Endpoints**
- `POST /generate` - Generate workflow from description
- `POST /modify` - Modify existing workflow
- `GET /validate` - Validate workflow JSON
- `GET /health` - System health check

## 🧪 Testing & Development

### **Run Tests**
```bash
# Run all tests
python -m pytest tests/

# Run specific test categories
python -m pytest tests/test_n8n_builder_unit.py
python -m pytest tests/test_integration.py
```

### **Development Scripts**
```bash
# Analyze project structure
python Scripts/analyze_project_files.py

# Generate process flow documentation
python Scripts/generate_process_flow.py

# Setup log rotation
.\Scripts\Setup-LogRotation.ps1 -Setup
```

## 📚 Documentation

### 🎯 **User Guides**
- **📖 [Complete Documentation](Documentation/README.md)** - Master guide
- **🔧 [Troubleshooting](Documentation/TROUBLESHOOTING.md)** - Fix common issues
- **⚡ [API Quick Reference](Documentation/api/API_QUICK_REFERENCE.md)** - Common examples

### 🔧 **For Developers**
- **📚 [API Documentation](Documentation/api/API_DOCUMENTATION.md)** - Complete reference
- **🏗️ [Technical Architecture](Documentation/technical/DOCUMENTATION.md)** - System design

### 🐳 **n8n-docker Setup**
- **⚡ [Lightning Start](n8n-docker/LIGHTNING_START.md)** - 2-minute setup
- **📖 [Complete Guide](n8n-docker/Documentation/README.md)** - Full reference

## 🚀 Recent Updates

### **🔧 System Improvements (Latest)**
- ✅ **Enhanced AI Generation** - Improved workflow quality and reliability
- ✅ **MCP Research Integration** - Real-time n8n documentation lookup
- ✅ **Better Error Handling** - Robust retry logic and fallback strategies
- ✅ **Comprehensive Testing Suite** - System health monitoring and validation framework
- ✅ **Smart Project Management** - Automated maintenance and optimization tools

### **🤖 AI & Integration**
- ✅ **AG-UI Protocol Support** - Advanced agent system integration
- ✅ **Enhanced Prompt Building** - Context-aware workflow generation
- ✅ **Workflow Validation** - Ensures n8n compatibility
- ✅ **Performance Optimization** - Faster response times and caching

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new features
4. Update documentation
5. Submit a pull request

## 📄 License

MIT License - See LICENSE file for details

---

**🎉 Ready to automate your workflows with AI?** Start with the [⚡ Lightning Start](#lightning-start) and be running in 2 minutes!

## 🌟 Powered by Augment Code

This project showcases the power of AI-assisted development using [Augment Code](https://augmentcode.com). The entire system was built with AI assistance, demonstrating:

- **Intelligent Code Generation**: AI-powered workflow creation
- **Smart Documentation**: Auto-generated guides and references  
- **Automated Testing**: Comprehensive test suites
- **Process Automation**: Scripts for maintenance and deployment

*Experience the future of development with AI as your coding partner.*
