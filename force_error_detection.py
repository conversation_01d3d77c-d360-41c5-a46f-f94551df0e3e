#!/usr/bin/env python3
"""
Force the Self-Healer to detect and process errors by manually triggering error detection.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

async def force_error_detection():
    print("=== FORCING SELF-HEALER ERROR DETECTION ===")
    
    try:
        from Self_Healer.core.error_monitor import ErrorMonitor
        
        # Initialize error monitor
        log_directory = Path("logs")
        error_monitor = ErrorMonitor(log_directory)
        
        print(f"\n1. INITIALIZING ERROR MONITOR:")
        print(f"   - Log directory: {log_directory}")
        print(f"   - Error log path: {error_monitor.error_log_path}")
        print(f"   - Main log path: {error_monitor.main_log_path}")
        
        # Force scan of log files
        print(f"\n2. FORCING LOG FILE SCAN:")
        
        # Manually scan the error log for recent entries
        if error_monitor.error_log_path.exists():
            with open(error_monitor.error_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            print(f"   - Total lines in error log: {len(lines)}")
            
            # Look for recent errors and manually process them
            recent_errors = []
            for line in lines[-50:]:  # Last 50 lines
                if "15:07:27" in line and "ERROR" in line:
                    recent_errors.append(line.strip())
            
            print(f"   - Recent errors found: {len(recent_errors)}")
            
            # Force rescan of logs to detect recent errors
            print(f"   - Forcing rescan of logs for last 2 hours...")
            await error_monitor.force_rescan_logs(hours_back=2)

            # Wait a moment for processing
            await asyncio.sleep(2)
        
        # Check current detected errors
        print(f"\n3. CURRENT DETECTED ERRORS:")
        print(f"   - Total detected: {len(error_monitor.detected_errors)}")
        
        if error_monitor.detected_errors:
            for error_id, detected_error in error_monitor.detected_errors.items():
                print(f"     * {error_id[:8]}: {detected_error.error_detail.title}")
                print(f"       - Severity: {detected_error.severity}")
                print(f"       - Category: {detected_error.category}")
                print(f"       - Frequency: {detected_error.frequency}")
                print(f"       - Age: {(datetime.now() - detected_error.timestamp).total_seconds():.1f}s")
        
        # Test get_new_errors with relaxed criteria
        print(f"\n4. TESTING NEW ERRORS CRITERIA:")
        new_errors = await error_monitor.get_new_errors()
        print(f"   - Errors meeting criteria: {len(new_errors)}")
        
        if new_errors:
            for error in new_errors:
                print(f"     ✅ {error.title}: {error.message[:80]}...")
        else:
            print("   - No errors meet the healing criteria")
            
            # Let's manually check why
            print("\n   🔍 MANUAL CRITERIA CHECK:")
            current_time = datetime.now()
            
            for error_id, detected_error in error_monitor.detected_errors.items():
                time_since = current_time - detected_error.timestamp
                is_recent = time_since.total_seconds() < 3600  # 1 hour
                is_critical = detected_error.severity in ['CRITICAL', 'ERROR']
                is_validation_error = 'validation' in detected_error.category.lower()
                is_workflow_error = 'workflow' in detected_error.category.lower()
                
                error_message = detected_error.error_detail.message.lower()
                is_validation_in_message = any(keyword in error_message for keyword in ['validation', 'trigger node', 'workflow failed'])
                
                meets_criteria = ((is_recent and is_critical) or 
                                (is_validation_error and is_recent) or 
                                (is_workflow_error and is_recent) or 
                                (is_validation_in_message and is_critical))
                
                print(f"     * {error_id[:8]}:")
                print(f"       - Recent: {is_recent} ({time_since.total_seconds():.1f}s)")
                print(f"       - Critical: {is_critical} ({detected_error.severity})")
                print(f"       - Validation error: {is_validation_error}")
                print(f"       - Workflow error: {is_workflow_error}")
                print(f"       - Validation in message: {is_validation_in_message}")
                print(f"       - MEETS CRITERIA: {meets_criteria}")
        
        # Force create a test error that should definitely be detected
        print(f"\n5. CREATING TEST ERROR:")
        
        # Write a test error to the error log
        test_error = f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]}] ERROR test_module: Test error for Self-Healer validation - workflow failed validation"
        
        with open(error_monitor.error_log_path, 'a', encoding='utf-8') as f:
            f.write(f"\n{test_error}\n")
        
        print(f"   - Test error written to log")
        
        # Force another rescan to pick up the test error
        await error_monitor.force_rescan_logs(hours_back=1)
        await asyncio.sleep(1)

        # Check if test error was detected
        new_errors_after = await error_monitor.get_new_errors()
        if len(new_errors_after) > len(new_errors):
            print(f"   ✅ Test error detected and meets healing criteria!")
        else:
            print(f"   ❌ Test error not detected or doesn't meet criteria")
        
        print(f"\n6. SUMMARY:")
        print(f"   - Error monitor initialized: ✅")
        print(f"   - Log files accessible: ✅")
        print(f"   - Recent errors in log: ✅")
        print(f"   - Errors detected by monitor: {'✅' if error_monitor.detected_errors else '❌'}")
        print(f"   - Errors meet healing criteria: {'✅' if new_errors else '❌'}")
        
        if not error_monitor.detected_errors:
            print(f"\n   🔍 ISSUE: Error monitor is not detecting errors from log file")
            print(f"      - Check if error patterns are correct")
            print(f"      - Check if log file scanning is working")
        elif not new_errors:
            print(f"\n   🔍 ISSUE: Errors detected but don't meet healing criteria")
            print(f"      - Consider relaxing criteria in get_new_errors()")
            print(f"      - Or ensure error categorization is correct")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(force_error_detection())
