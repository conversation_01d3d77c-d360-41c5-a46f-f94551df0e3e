"""
Test suite for Self-Healer stored procedures and database wrapper
"""

import asyncio
import pytest
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
from n8n_builder.mcp_database_tool import MCPDatabaseTool


class TestStoredProcedures:
    """Test suite for Self-Healer stored procedures."""
    
    @pytest.fixture
    async def knowledge_db(self):
        """Create a knowledge database wrapper instance."""
        return SelfHealerKnowledgeDB('knowledgebase')
    
    @pytest.fixture
    async def mcp_db(self):
        """Create an MCP database tool instance."""
        return MCPDatabaseTool('knowledgebase')
    
    async def test_database_connection(self, mcp_db):
        """Test basic database connectivity."""
        print("\n🔌 Testing Database Connection...")
        
        result = await mcp_db.test_connection()
        
        assert result['status'] == 'success', f"Connection failed: {result}"
        assert result['connected'] is True
        
        print(f"✅ Connected to: {result['database_name']}")
        print(f"   Server Version: {result['server_version'][:50]}...")
        print(f"   Connection Time: {result['connection_time']}")
    
    async def test_stored_procedure_creation(self, mcp_db):
        """Test that stored procedures exist in the database."""
        print("\n📋 Testing Stored Procedure Existence...")
        
        expected_procedures = [
            'S_SYS_XRF_EntityAttributeValue_P_EntityID',
            'S_SYS_REF_Fact_P_ErrorType',
            'S_SYS_REF_Opinion_P_ErrorType',
            'S_SYS_REF_Fact_SelfHealer_Analytics_Prms',
            'S_SYS_SelfHealer_KnowledgeSearch_Prms'
        ]
        
        # Check if procedures exist
        query = """
        SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
        """.format(','.join([f"'{proc}'" for proc in expected_procedures]))
        
        result = await mcp_db.execute_query(query)
        
        assert result['status'] == 'success', f"Query failed: {result}"
        
        found_procedures = [row['name'] for row in result['rows']]
        
        for proc in expected_procedures:
            if proc in found_procedures:
                print(f"✅ Found: {proc}")
            else:
                print(f"❌ Missing: {proc}")
        
        print(f"\nFound {len(found_procedures)} of {len(expected_procedures)} expected procedures")
    
    async def test_session_attributes_procedure(self, knowledge_db):
        """Test the session attributes stored procedure."""
        print("\n🔍 Testing Session Attributes Procedure...")
        
        # Test with a sample entity ID (assuming entity ID 1 exists)
        result = await knowledge_db.get_session_attributes(1)
        
        print(f"Result status: {result['status']}")
        
        if result['status'] == 'success':
            print(f"✅ Retrieved {result['attribute_count']} attributes for entity {result['entity_id']}")
            if result['attributes']:
                print("   Sample attributes:")
                for attr in result['attributes'][:3]:  # Show first 3
                    print(f"     - {attr.get('AttributeName', 'N/A')}: {attr.get('EntityValue', 'N/A')}")
        else:
            print(f"ℹ️  No attributes found (this is normal if entity 1 doesn't exist): {result.get('error', 'Unknown error')}")
    
    async def test_facts_by_error_type_procedure(self, knowledge_db):
        """Test the facts by error type stored procedure."""
        print("\n📊 Testing Facts by Error Type Procedure...")
        
        # Test with a common error type pattern
        test_error_types = ['JSON', 'Database', 'Network', 'Configuration']
        
        for error_type in test_error_types:
            result = await knowledge_db.get_facts_by_error_type(error_type)
            
            print(f"\nTesting error type: {error_type}")
            print(f"Result status: {result['status']}")
            
            if result['status'] == 'success':
                print(f"✅ Found {result['fact_count']} facts")
                if result['facts']:
                    print(f"   Average validity: {result['average_validity']:.2f}")
                    print("   Top fact:")
                    top_fact = result['facts'][0]
                    print(f"     - {top_fact.get('Name', 'N/A')} (Validity: {top_fact.get('ValidityRating', 'N/A')})")
            else:
                print(f"ℹ️  No facts found for {error_type}")
    
    async def test_opinions_by_error_type_procedure(self, knowledge_db):
        """Test the opinions by error type stored procedure."""
        print("\n💭 Testing Opinions by Error Type Procedure...")
        
        # Test with a common error type pattern
        result = await knowledge_db.get_opinions_by_error_type('Database')
        
        print(f"Result status: {result['status']}")
        
        if result['status'] == 'success':
            print(f"✅ Found {result['opinion_count']} opinions")
            if result['opinions']:
                print(f"   Average validity: {result['average_validity']:.2f}")
                print("   Sample opinion:")
                opinion = result['opinions'][0]
                print(f"     - {opinion.get('Name', 'N/A')}")
                print(f"       Opinion: {opinion.get('Opinion', 'N/A')[:100]}...")
        else:
            print(f"ℹ️  No opinions found: {result.get('error', 'Unknown error')}")
    
    async def test_solution_analytics_procedure(self, knowledge_db):
        """Test the solution analytics stored procedure."""
        print("\n📈 Testing Solution Analytics Procedure...")
        
        # Test with different parameters
        test_cases = [
            {'category': None, 'min_validity': 0, 'limit': 10},
            {'category': 'JSON', 'min_validity': 50.0, 'limit': 5},
            {'category': None, 'min_validity': 80.0, 'limit': 3}
        ]
        
        for i, params in enumerate(test_cases):
            print(f"\nTest case {i+1}: {params}")
            result = await knowledge_db.get_solution_analytics(**params)
            
            print(f"Result status: {result['status']}")
            
            if result['status'] == 'success':
                metrics = result['metrics']
                print(f"✅ Analytics retrieved:")
                print(f"   Total solutions: {metrics['total_solutions']}")
                print(f"   Trending solutions: {metrics['trending_count']}")
                print(f"   High validity solutions: {metrics['high_validity_count']}")
                print(f"   Average effectiveness: {metrics['average_effectiveness']:.2f}")
            else:
                print(f"ℹ️  No analytics data: {result.get('error', 'Unknown error')}")
    
    async def test_knowledge_search_procedure(self, knowledge_db):
        """Test the comprehensive knowledge search stored procedure."""
        print("\n🔍 Testing Knowledge Search Procedure...")
        
        # Test with different search queries
        test_queries = [
            {'search_query': 'error', 'knowledge_type': None},
            {'search_query': 'database', 'knowledge_type': 'fact'},
            {'search_query': 'connection', 'knowledge_type': 'opinion'}
        ]
        
        for i, params in enumerate(test_queries):
            print(f"\nSearch test {i+1}: {params}")
            result = await knowledge_db.search_knowledge(**params)
            
            print(f"Result status: {result['status']}")
            
            if result['status'] == 'success':
                counts = result['result_counts']
                print(f"✅ Search completed:")
                print(f"   Total results: {result['total_results']}")
                print(f"   Facts: {counts['facts']}")
                print(f"   Opinions: {counts['opinions']}")
                print(f"   Evidence: {counts['evidence']}")
            else:
                print(f"ℹ️  No search results: {result.get('error', 'Unknown error')}")


async def run_all_tests():
    """Run all stored procedure tests."""
    print("🧪 Self-Healer Stored Procedures Test Suite")
    print("=" * 60)
    
    test_suite = TestStoredProcedures()
    
    try:
        # Create instances
        knowledge_db = SelfHealerKnowledgeDB('knowledgebase')
        mcp_db = MCPDatabaseTool('knowledgebase')
        
        # Run tests
        await test_suite.test_database_connection(mcp_db)
        await test_suite.test_stored_procedure_creation(mcp_db)
        await test_suite.test_session_attributes_procedure(knowledge_db)
        await test_suite.test_facts_by_error_type_procedure(knowledge_db)
        await test_suite.test_opinions_by_error_type_procedure(knowledge_db)
        await test_suite.test_solution_analytics_procedure(knowledge_db)
        await test_suite.test_knowledge_search_procedure(knowledge_db)
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_all_tests())
