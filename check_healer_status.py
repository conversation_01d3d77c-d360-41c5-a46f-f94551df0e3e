#!/usr/bin/env python3
"""
Check the actual status of the Self-Healer Manager and see what data the dashboard should be showing.
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from Self_Healer.core.healer_manager import <PERSON><PERSON><PERSON>erManager


async def check_healer_status():
    """Check the healer manager status and compare with what dashboard should show."""
    print("=== Self-Healer Manager Status Check ===")
    
    # Initialize healer manager
    log_directory = Path("logs")
    healer_manager = SelfHealerManager(log_directory)
    
    print(f"Healer manager initialized: {healer_manager is not None}")
    print(f"Is running: {healer_manager.is_running}")
    print(f"Status: {healer_manager.status}")
    print()
    
    # Check if it's running
    if not healer_manager.is_running:
        print("⚠️  Healer manager is NOT running!")
        print("This explains why the dashboard shows no new activity.")
        print()
        
        print("=== Starting Healer Manager ===")
        await healer_manager.start()
        print(f"Started: {healer_manager.is_running}")
        print(f"New status: {healer_manager.status}")
        print()
    
    # Get status (what the dashboard API returns)
    print("=== Dashboard API Data (/api/status) ===")
    status = await healer_manager.get_status()
    print(json.dumps(status, indent=2, default=str))
    print()
    
    # Check error monitor status
    print("=== Error Monitor Status ===")
    error_monitor = healer_manager.error_monitor
    print(f"Error monitor running: {error_monitor.is_running}")
    print(f"Detected errors: {len(error_monitor.detected_errors)}")
    print(f"Error queue size: {error_monitor.error_queue.qsize()}")
    
    # Get error statistics
    error_stats = error_monitor.get_error_statistics()
    print(f"Error statistics: {json.dumps(error_stats, indent=2, default=str)}")
    print()
    
    # Check for new errors that should trigger healing
    print("=== New Errors Ready for Healing ===")
    new_errors = await error_monitor.get_new_errors()
    print(f"New errors count: {len(new_errors)}")
    
    for i, error in enumerate(new_errors):
        print(f"  Error {i+1}:")
        print(f"    Title: {error.title}")
        print(f"    Message: {error.message}")
        print(f"    Category: {error.category}")
        print(f"    Severity: {error.severity}")
        print()
    
    # Check active sessions
    print("=== Active Healing Sessions ===")
    print(f"Active sessions: {len(healer_manager.active_sessions)}")
    for session_id, session in healer_manager.active_sessions.items():
        print(f"  Session {session_id}:")
        print(f"    Status: {session.status}")
        print(f"    Error ID: {session.error_id}")
        print(f"    Start time: {session.start_time}")
        print()
    
    # Check session history
    print("=== Session History ===")
    print(f"Total sessions in history: {len(healer_manager.session_history)}")
    for session in healer_manager.session_history[-5:]:  # Last 5 sessions
        print(f"  Session {session.session_id}:")
        print(f"    Status: {session.status}")
        print(f"    Success: {session.success}")
        print(f"    Duration: {(session.end_time or session.start_time) - session.start_time}")
        print()
    
    # Check metrics
    print("=== Healer Manager Metrics ===")
    metrics = healer_manager.metrics
    print(f"Total errors detected: {metrics['total_errors_detected']}")
    print(f"Total healing attempts: {metrics['total_healing_attempts']}")
    print(f"Successful healings: {metrics['successful_healings']}")
    print(f"Failed healings: {metrics['failed_healings']}")
    print()
    
    # Force a healing cycle if there are new errors
    if new_errors and healer_manager.is_running:
        print("=== Triggering Healing Cycle ===")
        print("Found new errors and healer is running - should trigger healing...")
        
        # Wait a bit to see if healing starts automatically
        await asyncio.sleep(5)
        
        # Check status again
        updated_status = await healer_manager.get_status()
        print("Updated status after waiting:")
        print(json.dumps(updated_status, indent=2, default=str))
    
    # Stop healer manager
    if healer_manager.is_running:
        await healer_manager.stop()
        print("Healer manager stopped.")


if __name__ == "__main__":
    asyncio.run(check_healer_status())
