# N8N_Builder: AI-Powered Workflow Automation

🤖 **Transform plain English into powerful N8N workflows using AI**

> **Built with Augment Code** - Demonstrating advanced AI-assisted development capabilities

## 🏷️ Editions

**N8N_Builder** is available in two editions:

| Edition | Description | Best For |
|---------|-------------|----------|
| **🌟 Community Edition** | Full AI workflow generation with standard error handling | Individual developers, learning, open source projects |
| **🚀 Enterprise Edition** | Enhanced with advanced monitoring and automated recovery systems | Production environments, enterprise deployments |

> **Note**: This repository contains the Community Edition. Enterprise features are available separately for production deployments.

## 🚀 Quick Start (Choose Your Speed)

| Time Available | Start Here | What You'll Get |
|----------------|------------|-----------------|
| **2 minutes** | [⚡ Lightning Start](LIGHTNING_START.md) | Working system, no explanations |
| **15 minutes** | [📖 Getting Started](GETTING_STARTED.md) | Understanding + customization |
| **30 minutes** | [🎯 First Workflow](Documentation/guides/FIRST_WORKFLOW.md) | Complete workflow tutorial |

## 🏗️ How It Works

```mermaid
graph LR
    A[Describe in English] --> B[AI Generates JSON]
    B --> C[Import to n8n]
    C --> D[Workflow Runs]

    classDef process fill:#e8f5e8
    class A,B,C,D process
```

**Complete System:**
1. **🤖 N8N_Builder** (this repo) - AI workflow generator
2. **🐳 n8n-docker** - Production execution environment
3. **🔄 Integration** - Seamless workflow transfer
4. **🚀 Enterprise Features** - Advanced monitoring and recovery (separate deployment)

## ✨ What You Can Build

**💡 Example Automations:**
- *"Send me an email when a new file is uploaded to my folder"*
- *"Post to Twitter when I publish a new blog article"*
- *"Convert CSV files to JSON and send to a webhook"*
- *"Alert me when my website goes down"*
- *"Send welcome emails to new customers"*

## 🎯 Key Features

### 🌟 **Community Edition Features**
- **🤖 AI-Powered**: Convert plain English to n8n workflows
- **🔍 Smart Research**: Real-time n8n documentation lookup
- **⚡ Dual APIs**: Standard REST + AG-UI Protocol
- **✅ Validation**: Ensures workflows meet n8n standards
- **🔄 Iteration**: Modify existing workflows easily
- **🌐 Web Interface**: User-friendly workflow generation
- **🏭 Production Ready**: Complete Docker execution environment
- **🔧 Basic Error Handling**: Standard retry logic and error logging
- **🧪 Testing Suite**: Core system validation and health checks

### 🚀 **Enterprise Edition Enhancements**
- **📊 Advanced Monitoring**: Real-time system health dashboards
- **🛡️ Automated Recovery**: Intelligent error detection and resolution
- **🗄️ Enhanced Database**: Advanced data management with stored procedures
- **📋 Log Management**: 24-hour rotation with compression and retention
- **🧹 Smart Maintenance**: Automated project optimization and cleanup
- **🔄 Automated Recovery**: Proactive system maintenance and recovery

## 🚀 Getting Started

### **🌟 Community Edition (This Repository)**

**Run the Community Edition:**
```bash
# Start N8N Builder Community Edition
python run_public.py
```

**Basic Health Check:**
```bash
# Run core system tests
python tests/test_system_health.py
```

### **🚀 Enterprise Edition**

Enterprise Edition includes advanced monitoring, automated recovery, and enhanced database features. Contact for deployment information.

**Enterprise Features Include:**
- Advanced monitoring dashboards
- Automated error detection and recovery
- Enhanced database management
- 24-hour log rotation and management
- Intelligent system maintenance

## 🔧 Running Different Editions

### **🌟 Community Edition (Default)**
```bash
# Standard startup (Community Edition)
python run_public.py

# Available at: http://localhost:8002
# Features: Full AI workflow generation with standard error handling
```

### **🚀 Enterprise Edition**
```bash
# Full system with advanced features (requires Enterprise components)
python run.py

# Available at:
# - Main App: http://localhost:8002
# - Advanced Dashboard: http://localhost:8081 (Enterprise only)
```

### **🔍 How to Tell Which Edition You're Running**
- **Community**: Standard error logging, basic retry logic
- **Enterprise**: Advanced monitoring dashboard, automated recovery system

## 📚 Documentation

### 🎯 **Start Here**
- **📖 [Complete Documentation](Documentation/README.md)** - Master guide
- **🔧 [Troubleshooting](Documentation/TROUBLESHOOTING.md)** - Fix common issues

### 🔧 **For Developers**
- **📚 [API Documentation](Documentation/api/API_DOCUMENTATION.md)** - Complete reference
- **🏗️ [Technical Architecture](Documentation/technical/DOCUMENTATION.md)** - System design

### 🐳 **n8n-docker Setup**
- **⚡ [Lightning Start](n8n-docker/LIGHTNING_START.md)** - 2-minute setup
- **📖 [Complete Guide](n8n-docker/Documentation/README.md)** - Full reference

### 🤖 **Advanced Features**
- **🚀 [Enterprise Features](Documentation/ADVANCED_FEATURES.md)** - Enhanced monitoring and recovery

## 🚀 Recent Updates

### **🌟 Community Edition (Latest)**
- ✅ **Enhanced AI Generation** - Improved workflow quality and reliability
- ✅ **MCP Research Integration** - Real-time n8n documentation lookup
- ✅ **Better Error Handling** - Robust retry logic and fallback strategies
- ✅ **Dual API Support** - Standard REST + AG-UI Protocol
- ✅ **Improved Validation** - Enhanced workflow structure checking
- ✅ **Docker Integration** - Streamlined n8n-docker setup

### **🚀 Enterprise Edition Features**
- ✅ **Advanced Monitoring** - Real-time system health dashboards
- ✅ **Automated Recovery** - Intelligent error detection and resolution
- ✅ **Database Enhancement** - Stored procedures for optimal performance
- ✅ **Log Management** - 24-hour rotation with compression and retention
- ✅ **Smart Maintenance** - Automated project optimization and cleanup

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new features
4. Update documentation
5. Submit a pull request

## 📄 License

MIT License - See LICENSE file for details

## 📊 Development Scope

N8N_Builder represents a substantial development effort with a comprehensive, production-ready architecture:

### **🎯 Codebase Statistics**
- **📁 Total Custom Python Files**: 124 files
- **🌟 Community Edition**: 109 files (88%)
- **🚀 Enterprise Modules**: 15 files (12%)

### **📈 Architecture Breakdown**
| Component | Files | Purpose |
|-----------|-------|---------|
| **🤖 Core AI Engine** | 48 files | Workflow generation, validation, optimization |
| **🧪 Testing Suite** | 31 files | Comprehensive validation and quality assurance |
| **🔧 Utilities & Scripts** | 16 files | Project management and automation tools |
| **⚙️ Setup & Configuration** | 14 files | Installation, deployment, and runtime management |
| **🚀 Enterprise Modules** | 15 files | Advanced monitoring and intelligent recovery systems |

### **🏗️ Development Highlights**
- **🎯 Production Architecture**: Modular design with clean separation of concerns
- **✅ Comprehensive Testing**: 25% of codebase dedicated to quality assurance
- **🔄 Dual-Mode Design**: Community and Enterprise editions from single codebase
- **🛡️ Enterprise Ready**: Advanced monitoring, recovery, and maintenance capabilities
- **📚 Well Documented**: Extensive documentation and setup guides

### **🌟 Technical Excellence**
- **🏛️ Clean Architecture**: Follows enterprise software development patterns
- **🔌 Modular Components**: Plug-and-play enterprise modules
- **🧪 Test-Driven**: Robust testing framework ensuring reliability
- **📈 Scalable Design**: Built for both individual and enterprise deployment

---

**🎉 Ready to automate your workflows with AI?** Start with [⚡ Lightning Start](LIGHTNING_START.md) and be running in 2 minutes!