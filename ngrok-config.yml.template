version: "3"
agent:
    # IMPORTANT: Replace with your actual nGrok auth token
    # Get your token from: https://dashboard.ngrok.com/get-started/your-authtoken
    authtoken: YOUR_NGROK_AUTH_TOKEN_HERE

# Tunnel configurations for n8n
tunnels:
  n8n:
    proto: http
    addr: 5678
    # Use a custom subdomain if you have a paid plan, otherwise comment out the next line
    # subdomain: your-custom-subdomain
    # Optional: Add custom headers for better integration
    request_header:
      add:
        - "X-Forwarded-Proto: https"
    # Optional: Enable inspection for debugging
    inspect: true

  # Alternative configuration for development (simpler)
  n8n-dev:
    proto: http
    addr: 5678
    inspect: true

# Additional tunnel examples (uncomment and customize as needed)
#
# # For multiple services
# api:
#   proto: http
#   addr: 3000
#   subdomain: my-api  # Requires paid plan
#
# # For HTTPS with custom domain (requires paid plan)
# secure-n8n:
#   proto: http
#   addr: 5678
#   hostname: n8n.yourdomain.com
#
# # For TCP tunnels
# database:
#   proto: tcp
#   addr: 5432
