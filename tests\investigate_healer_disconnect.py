#!/usr/bin/env python3
"""
Comprehensive Investigation: Self-Healer Dashboard Disconnect Issue
================================================================

This script performs a deep investigation to identify why the Self-Healer dashboard
shows 0 errors while the error monitor detects errors successfully.

The issue: Error monitor detects errors → Dashboard shows 0 errors
Goal: Find the exact disconnection point in the data flow
"""

import asyncio
import sys
import requests
import json
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from Self_Healer.core.error_monitor import ErrorMonitor
from Self_Healer.core.knowledge_integration import KnowledgeBaseIntegrator


async def investigate_healer_disconnect():
    """Comprehensive investigation of the Self-Healer disconnect issue."""
    print("=" * 80)
    print("COMPREHENSIVE SELF-HEALER DISCONNECT INVESTIGATION")
    print("=" * 80)
    print(f"Investigation started at: {datetime.now()}")
    print()
    
    # PHASE 1: Service Status Investigation
    print("PHASE 1: SERVICE STATUS INVESTIGATION")
    print("-" * 50)
    
    services = {
        "N8N Builder": "http://localhost:8002/",
        "Self-Healer Dashboard": "http://localhost:8081/api/status"
    }
    
    service_status = {}
    for service_name, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            service_status[service_name] = {
                "status": "UP",
                "status_code": response.status_code,
                "response_size": len(response.content)
            }
            print(f"✅ {service_name}: UP (status: {response.status_code})")
        except Exception as e:
            service_status[service_name] = {
                "status": "DOWN",
                "error": str(e)
            }
            print(f"❌ {service_name}: DOWN - {e}")
    
    # PHASE 2: Error Monitor Investigation
    print(f"\nPHASE 2: ERROR MONITOR INVESTIGATION")
    print("-" * 50)
    
    try:
        # Initialize error monitor
        error_monitor = ErrorMonitor()
        await error_monitor.start()
        print("✅ Error Monitor initialized and started")
        
        # Force a rescan to detect current errors
        print("🔍 Forcing error rescan...")
        initial_errors = len(error_monitor.detected_errors)
        print(f"   Initial detected errors: {initial_errors}")
        
        # Force rescan of the last hour
        await error_monitor.force_rescan_logs(hours_back=1)
        
        final_errors = len(error_monitor.detected_errors)
        new_errors = final_errors - initial_errors
        print(f"   Final detected errors: {final_errors}")
        print(f"   New errors found: {new_errors}")
        
        # Get error details
        if error_monitor.detected_errors:
            print(f"\n📋 Error Monitor Details:")
            print(f"   Total errors in memory: {len(error_monitor.detected_errors)}")
            print(f"   Error categories:")
            categories = {}
            for error in error_monitor.detected_errors.values():
                cat = error.get('category', 'Unknown')
                categories[cat] = categories.get(cat, 0) + 1
            for cat, count in categories.items():
                print(f"     {cat}: {count}")
        
        await error_monitor.stop()
        
    except Exception as e:
        print(f"❌ Error Monitor investigation failed: {e}")
        return
    
    # PHASE 3: Dashboard API Deep Dive
    print(f"\nPHASE 3: DASHBOARD API DEEP DIVE")
    print("-" * 50)
    
    dashboard_endpoints = [
        "/api/status",
        "/api/metrics", 
        "/api/sessions",
        "/api/learning"
    ]
    
    dashboard_data = {}
    for endpoint in dashboard_endpoints:
        try:
            url = f"http://localhost:8081{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    dashboard_data[endpoint] = data
                    print(f"✅ {endpoint}: OK - {len(str(data))} chars")
                except:
                    dashboard_data[endpoint] = response.text
                    print(f"✅ {endpoint}: OK - Non-JSON response")
            else:
                print(f"❌ {endpoint}: Status {response.status_code}")
                dashboard_data[endpoint] = {"error": f"Status {response.status_code}"}
                
        except Exception as e:
            print(f"❌ {endpoint}: {e}")
            dashboard_data[endpoint] = {"error": str(e)}
    
    # PHASE 4: Data Flow Analysis
    print(f"\nPHASE 4: DATA FLOW ANALYSIS")
    print("-" * 50)
    
    if "/api/status" in dashboard_data and isinstance(dashboard_data["/api/status"], dict):
        status_data = dashboard_data["/api/status"]
        
        print(f"📊 Dashboard Status Analysis:")
        print(f"   System Status: {status_data.get('status', 'Unknown')}")
        print(f"   Is Running: {status_data.get('is_running', 'Unknown')}")
        print(f"   Total Errors Detected: {status_data.get('metrics', {}).get('total_errors_detected', 'Unknown')}")
        print(f"   Total Healing Attempts: {status_data.get('metrics', {}).get('total_healing_attempts', 'Unknown')}")
        print(f"   Active Sessions: {status_data.get('metrics', {}).get('active_sessions', 'Unknown')}")
        
        # Critical Analysis
        dashboard_errors = status_data.get('metrics', {}).get('total_errors_detected', 0)
        monitor_errors = final_errors
        
        print(f"\n🔍 CRITICAL DISCONNECT ANALYSIS:")
        print(f"   Error Monitor detected: {monitor_errors} errors")
        print(f"   Dashboard shows: {dashboard_errors} errors")
        print(f"   Disconnect magnitude: {monitor_errors - dashboard_errors}")
        
        if monitor_errors > 0 and dashboard_errors == 0:
            print(f"   ⚠️  CONFIRMED: Complete disconnect between error detection and dashboard")
        elif monitor_errors != dashboard_errors:
            print(f"   ⚠️  PARTIAL: Partial disconnect - some errors not reaching dashboard")
        else:
            print(f"   ✅ SYNCHRONIZED: Error counts match")
    
    # PHASE 5: Instance Investigation
    print(f"\nPHASE 5: HEALER MANAGER INSTANCE INVESTIGATION")
    print("-" * 50)
    
    # Check if there are multiple healer manager instances
    try:
        # Look for evidence of multiple instances in logs
        log_files = ["logs/n8n_builder.log", "logs/errors.log"]
        
        for log_file in log_files:
            if Path(log_file).exists():
                with open(log_file, 'r') as f:
                    content = f.read()
                    
                # Count healer manager initializations
                init_count = content.count("Self-Healer Manager initialized")
                start_count = content.count("Self-Healer Manager started successfully")
                
                print(f"📄 {log_file}:")
                print(f"   Healer Manager initializations: {init_count}")
                print(f"   Healer Manager starts: {start_count}")
                
                if init_count > 1:
                    print(f"   ⚠️  Multiple initializations detected - possible instance conflict")
    
    except Exception as e:
        print(f"❌ Instance investigation failed: {e}")
    
    # PHASE 6: Memory vs Persistence Investigation
    print(f"\nPHASE 6: MEMORY VS PERSISTENCE INVESTIGATION")
    print("-" * 50)
    
    try:
        # Check if errors are being persisted vs just held in memory
        print("🔍 Investigating error persistence...")
        
        # Check for any error storage files
        possible_storage = [
            "Self_Healer/data/errors.json",
            "Self_Healer/data/sessions.json", 
            "cache/errors.json",
            "logs/healer_errors.json"
        ]
        
        for storage_path in possible_storage:
            if Path(storage_path).exists():
                try:
                    with open(storage_path, 'r') as f:
                        data = json.load(f)
                        print(f"✅ Found storage: {storage_path} - {len(data)} items")
                except:
                    print(f"⚠️  Found file: {storage_path} - but couldn't parse as JSON")
            else:
                print(f"❌ No storage found: {storage_path}")
    
    except Exception as e:
        print(f"❌ Persistence investigation failed: {e}")
    
    # PHASE 7: Recommendations
    print(f"\nPHASE 7: DIAGNOSTIC RECOMMENDATIONS")
    print("-" * 50)
    
    print("🎯 FINDINGS SUMMARY:")
    print(f"   - Error Monitor: {'Working' if final_errors > 0 else 'Not detecting errors'}")
    print(f"   - Dashboard API: {'Accessible' if service_status.get('Self-Healer Dashboard', {}).get('status') == 'UP' else 'Inaccessible'}")
    print(f"   - Data Synchronization: {'BROKEN' if monitor_errors > dashboard_errors else 'Working'}")
    
    print(f"\n🔧 RECOMMENDED ACTIONS:")
    
    if monitor_errors > 0 and dashboard_errors == 0:
        print("   1. CRITICAL: Complete data flow disconnect")
        print("      → Check healer manager instance synchronization")
        print("      → Verify error data is being passed to dashboard backend")
        print("      → Check for multiple healer manager instances")
        
    if service_status.get('Self-Healer Dashboard', {}).get('status') != 'UP':
        print("   2. Dashboard service is down")
        print("      → Restart Self-Healer dashboard")
        print("      → Check dashboard startup logs for errors")
        
    print("   3. Investigate healer manager data sharing")
    print("      → Check if error monitor and dashboard share the same healer manager instance")
    print("      → Verify error data persistence mechanism")
    
    print(f"\nInvestigation completed at: {datetime.now()}")


if __name__ == "__main__":
    asyncio.run(investigate_healer_disconnect())
