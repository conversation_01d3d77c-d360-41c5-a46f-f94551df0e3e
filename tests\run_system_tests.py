"""
System Test Runner - Quick execution of all system tests
"""

import asyncio
import sys
import os
import time
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_system_health import run_system_health_check
from test_stored_procedures import run_all_tests as run_stored_procedure_tests


class SystemTestRunner:
    """Orchestrates all system tests and provides summary reporting."""
    
    def __init__(self):
        self.start_time = None
        self.test_results = {}
        self.overall_status = 'UNKNOWN'
    
    async def run_all_tests(self):
        """Run all system test suites."""
        self.start_time = datetime.now()
        
        print("🧪 N8N Builder System Test Suite")
        print("=" * 80)
        print(f"Started at: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Test Suite 1: System Health Check
        print("🏥 Running System Health Check...")
        try:
            health_results = await run_system_health_check()
            self.test_results['system_health'] = {
                'status': health_results['overall_status'],
                'details': health_results,
                'duration': self._calculate_duration(health_results.get('timestamp'))
            }
            print(f"✅ System Health Check completed: {health_results['overall_status']}")
        except Exception as e:
            print(f"❌ System Health Check failed: {e}")
            self.test_results['system_health'] = {
                'status': 'FAILED',
                'error': str(e),
                'duration': 0
            }
        
        print("\n" + "="*80)
        
        # Test Suite 2: Stored Procedures Test
        print("🗄️ Running Stored Procedures Test...")
        try:
            await run_stored_procedure_tests()
            self.test_results['stored_procedures'] = {
                'status': 'COMPLETED',
                'details': 'See console output above',
                'duration': 0  # Would need to modify the test to return timing
            }
            print("✅ Stored Procedures Test completed")
        except Exception as e:
            print(f"❌ Stored Procedures Test failed: {e}")
            self.test_results['stored_procedures'] = {
                'status': 'FAILED',
                'error': str(e),
                'duration': 0
            }
        
        print("\n" + "="*80)
        
        # Additional quick tests
        await self._run_quick_integration_tests()
        
        # Calculate overall status
        self._calculate_overall_status()
        
        # Print final summary
        self._print_final_summary()
        
        return self.test_results
    
    async def _run_quick_integration_tests(self):
        """Run quick integration tests for critical components."""
        print("⚡ Running Quick Integration Tests...")
        
        test_start = time.time()
        integration_results = {
            'import_tests': 'UNKNOWN',
            'config_tests': 'UNKNOWN',
            'basic_functionality': 'UNKNOWN'
        }
        
        # Test 1: Import Tests
        try:
            from n8n_builder.n8n_builder import N8NBuilder
            from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
            from n8n_builder.mcp_database_tool import MCPDatabaseTool
            
            integration_results['import_tests'] = 'PASS'
            print("✅ Import Tests: All critical modules imported successfully")
        except Exception as e:
            integration_results['import_tests'] = 'FAIL'
            print(f"❌ Import Tests: Failed to import modules - {e}")
        
        # Test 2: Configuration Tests
        try:
            from n8n_builder.config import Config
            config = Config()
            
            # Check if basic config attributes exist
            has_mcp_db = hasattr(config, 'mcp_database')
            has_mcp_research = hasattr(config, 'mcp_research')
            
            if has_mcp_db and has_mcp_research:
                integration_results['config_tests'] = 'PASS'
                print("✅ Config Tests: Configuration loaded successfully")
            else:
                integration_results['config_tests'] = 'WARNING'
                print("⚠️ Config Tests: Some configuration sections missing")
                
        except Exception as e:
            integration_results['config_tests'] = 'FAIL'
            print(f"❌ Config Tests: Configuration loading failed - {e}")
        
        # Test 3: Basic Functionality
        try:
            # Test basic N8N Builder instantiation
            from n8n_builder.n8n_builder import N8NBuilder
            builder = N8NBuilder()
            
            # Test basic workflow structure
            test_workflow = {
                "name": "Test Workflow",
                "nodes": [],
                "connections": {}
            }
            
            # This is a very basic test - just checking if the builder can handle a simple workflow
            if hasattr(builder, 'generate_workflow') or hasattr(builder, 'build_workflow'):
                integration_results['basic_functionality'] = 'PASS'
                print("✅ Basic Functionality: N8N Builder instantiated successfully")
            else:
                integration_results['basic_functionality'] = 'WARNING'
                print("⚠️ Basic Functionality: N8N Builder missing expected methods")
                
        except Exception as e:
            integration_results['basic_functionality'] = 'FAIL'
            print(f"❌ Basic Functionality: N8N Builder test failed - {e}")
        
        test_duration = time.time() - test_start
        
        self.test_results['quick_integration'] = {
            'status': self._determine_integration_status(integration_results),
            'details': integration_results,
            'duration': round(test_duration, 2)
        }
        
        print(f"⚡ Quick Integration Tests completed in {test_duration:.2f}s")
    
    def _determine_integration_status(self, results):
        """Determine overall status from integration test results."""
        if all(status == 'PASS' for status in results.values()):
            return 'PASS'
        elif any(status == 'FAIL' for status in results.values()):
            return 'FAIL'
        else:
            return 'WARNING'
    
    def _calculate_duration(self, timestamp_str):
        """Calculate duration from timestamp string."""
        try:
            if timestamp_str:
                test_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                return (datetime.now() - test_time).total_seconds()
        except:
            pass
        return 0
    
    def _calculate_overall_status(self):
        """Calculate overall test suite status."""
        statuses = []
        for test_suite in self.test_results.values():
            status = test_suite.get('status', 'UNKNOWN')
            if status in ['FAILED', 'FAIL']:
                statuses.append('CRITICAL')
            elif status in ['WARNING', 'CRITICAL']:
                statuses.append('WARNING')
            elif status in ['PASS', 'COMPLETED', 'HEALTHY']:
                statuses.append('HEALTHY')
            else:
                statuses.append('UNKNOWN')
        
        if 'CRITICAL' in statuses:
            self.overall_status = 'CRITICAL'
        elif 'WARNING' in statuses:
            self.overall_status = 'WARNING'
        elif all(s == 'HEALTHY' for s in statuses):
            self.overall_status = 'HEALTHY'
        else:
            self.overall_status = 'UNKNOWN'
    
    def _print_final_summary(self):
        """Print final test summary."""
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        print("\n" + "="*80)
        print("📊 FINAL TEST SUMMARY")
        print("="*80)
        
        status_emoji = {
            'HEALTHY': '✅',
            'WARNING': '⚠️',
            'CRITICAL': '❌',
            'UNKNOWN': '❓'
        }
        
        print(f"{status_emoji.get(self.overall_status, '❓')} Overall Status: {self.overall_status}")
        print(f"🕒 Total Duration: {total_duration:.2f} seconds")
        print(f"📅 Completed: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📋 Test Suite Results:")
        for suite_name, suite_data in self.test_results.items():
            status = suite_data.get('status', 'UNKNOWN')
            duration = suite_data.get('duration', 0)
            emoji = {
                'PASS': '✅', 'COMPLETED': '✅', 'HEALTHY': '✅',
                'WARNING': '⚠️', 'CRITICAL': '⚠️',
                'FAIL': '❌', 'FAILED': '❌',
                'UNKNOWN': '❓'
            }.get(status, '❓')
            
            print(f"   {emoji} {suite_name.replace('_', ' ').title()}: {status} ({duration:.2f}s)")
            
            if 'error' in suite_data:
                print(f"      Error: {suite_data['error']}")
        
        # Recommendations
        print("\n💡 Recommendations:")
        if self.overall_status == 'CRITICAL':
            print("   - Address critical failures before proceeding")
            print("   - Check database connectivity and configuration")
            print("   - Verify all required components are installed")
        elif self.overall_status == 'WARNING':
            print("   - Review warnings and consider addressing them")
            print("   - Monitor system resources")
            print("   - Check log files for additional details")
        else:
            print("   - System appears healthy")
            print("   - Consider running tests periodically")
            print("   - Monitor performance metrics")
        
        print("\n📁 Test results saved to: tests/integration_results/")
        print("="*80)


async def main():
    """Main entry point for system test runner."""
    runner = SystemTestRunner()
    results = await runner.run_all_tests()
    
    # Exit with appropriate code
    if runner.overall_status == 'CRITICAL':
        sys.exit(1)
    elif runner.overall_status == 'WARNING':
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
