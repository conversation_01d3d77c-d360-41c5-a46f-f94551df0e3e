{"name": "Basic Webhook Workflow", "nodes": [{"id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "parameters": {"httpMethod": "POST", "path": "basic-workflow", "responseMode": "responseNode"}, "position": [250, 300]}, {"id": "process-data", "name": "Process Data", "type": "n8n-nodes-base.function", "parameters": {"functionCode": "// Process incoming data\nconst data = $input.first().json;\nreturn [{ json: { ...data, processed: true, timestamp: new Date().toISOString() } }];"}, "position": [500, 300]}, {"id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.httpResponse", "parameters": {"responseMode": "json", "responseBody": "{\"status\": \"success\", \"message\": \"Data processed\"}"}, "position": [750, 300]}, {"id": "sql-connect", "name": "Connect to SQL Server", "type": "database.sqlServer.connect", "parameters": {"host": "localhost", "username": "<PERSON><PERSON>", "password": "xyz824"}, "position": [600, 300]}, {"id": "sql-query", "name": "Run SQL Query", "type": "database.sqlServer.query", "parameters": {"query": "SELECT * FROM ElthosRPG.dbo.Table1"}, "position": [800, 300]}], "connections": {"webhook-trigger": {"main": [[{"node": "process-data", "type": "main", "index": 0}]]}, "process-data": {"main": [[{"node": "sql-connect", "type": "main", "index": 0}]]}, "sql-connect": {"main": [[{"node": "sql-query", "type": "main", "index": 0}]]}, "sql-query": {"main": [[{"node": "webhook-response", "type": "main", "index": 0}]]}}, "data_flow": [{"from": "Webhook Trigger", "to": "Process Data", "type": "main"}, {"from": "Process Data", "to": "Webhook Response", "type": "main"}], "settings": {}, "active": true, "version": 1}