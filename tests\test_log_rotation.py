"""
Test suite for 24-hour log rotation system
"""

import asyncio
import pytest
import sys
import os
import tempfile
import shutil
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from n8n_builder.log_rotation_manager import LogRotationManager, DatestampedTimedRotatingFileHandler


class TestLogRotationManager:
    """Test suite for the log rotation manager."""
    
    @pytest.fixture
    def temp_log_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def rotation_manager(self, temp_log_dir):
        """Create a log rotation manager instance for testing."""
        return LogRotationManager(temp_log_dir)
    
    def test_log_rotation_manager_initialization(self, rotation_manager, temp_log_dir):
        """Test that the log rotation manager initializes correctly."""
        assert rotation_manager.log_directory == temp_log_dir
        assert rotation_manager.backup_count == 30
        assert rotation_manager.compress_logs is True
        assert rotation_manager.rotation_time == "00:00"
        assert not rotation_manager.is_running
    
    def test_default_log_configs(self, rotation_manager):
        """Test that default log configurations are correct."""
        configs = rotation_manager._get_default_log_configs()
        
        expected_logs = ['main', 'errors', 'self_healer', 'performance', 'debug']
        assert all(log_name in configs for log_name in expected_logs)
        
        # Check main log config
        main_config = configs['main']
        assert main_config['filename'] == 'n8n_builder.log'
        assert main_config['level'] == 'INFO'
        assert 'format' in main_config
        
        # Check errors log config
        errors_config = configs['errors']
        assert errors_config['filename'] == 'errors.log'
        assert errors_config['level'] == 'ERROR'
    
    def test_single_log_rotation_setup(self, rotation_manager, temp_log_dir):
        """Test setting up rotation for a single log file."""
        config = {
            'filename': 'test.log',
            'level': 'INFO',
            'format': '%(asctime)s - %(message)s'
        }
        
        rotation_manager._setup_single_log_rotation('test', config)
        
        assert 'test' in rotation_manager.managed_handlers
        handler = rotation_manager.managed_handlers['test']
        assert isinstance(handler, DatestampedTimedRotatingFileHandler)
        assert handler.baseFilename == str(temp_log_dir / 'test.log')
    
    def test_log_statistics(self, rotation_manager, temp_log_dir):
        """Test getting log statistics."""
        # Create a test log file
        test_log = temp_log_dir / 'test.log'
        test_log.write_text('Test log content\n')
        
        # Set up rotation for the test log
        config = {'filename': 'test.log', 'level': 'INFO', 'format': '%(message)s'}
        rotation_manager._setup_single_log_rotation('test', config)
        
        # Get statistics
        stats = rotation_manager.get_log_statistics()
        
        assert 'test' in stats
        test_stats = stats['test']
        assert 'current_size_mb' in test_stats
        assert 'last_modified' in test_stats
        assert 'backup_files' in test_stats
        assert test_stats['rotation_enabled'] is True
    
    def test_cleanup_old_logs(self, rotation_manager, temp_log_dir):
        """Test cleaning up old log files."""
        # Create some test log files with different ages
        current_time = datetime.now()
        
        # Recent file (should not be deleted)
        recent_log = temp_log_dir / 'recent.log'
        recent_log.write_text('Recent log')
        
        # Old file (should be deleted)
        old_log = temp_log_dir / 'old.log'
        old_log.write_text('Old log')
        
        # Set the old file's modification time to 35 days ago
        old_time = current_time - timedelta(days=35)
        old_timestamp = old_time.timestamp()
        os.utime(old_log, (old_timestamp, old_timestamp))
        
        # Run cleanup (keep files newer than 30 days)
        rotation_manager.cleanup_old_logs(days_to_keep=30)
        
        # Check results
        assert recent_log.exists()
        assert not old_log.exists()


class TestDatestampedTimedRotatingFileHandler:
    """Test suite for the custom rotating file handler."""
    
    @pytest.fixture
    def temp_log_file(self):
        """Create a temporary log file for testing."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.log')
        temp_file.close()
        yield temp_file.name
        try:
            os.unlink(temp_file.name)
        except FileNotFoundError:
            pass
    
    def test_handler_initialization(self, temp_log_file):
        """Test that the handler initializes correctly."""
        handler = DatestampedTimedRotatingFileHandler(
            temp_log_file,
            when='midnight',
            interval=1,
            backupCount=7,
            compress=True
        )
        
        assert handler.baseFilename == temp_log_file
        assert handler.when == 'MIDNIGHT'
        assert handler.interval == 1
        assert handler.backupCount == 7
        assert handler.compress is True
        assert handler.datestamp_format == '%Y%m%d'
    
    def test_handler_logging(self, temp_log_file):
        """Test that the handler can log messages."""
        handler = DatestampedTimedRotatingFileHandler(
            temp_log_file,
            when='midnight',
            interval=1,
            backupCount=7
        )
        
        # Create a logger and add the handler
        logger = logging.getLogger('test_rotation')
        logger.setLevel(logging.INFO)
        logger.addHandler(handler)
        
        # Log some messages
        logger.info('Test message 1')
        logger.info('Test message 2')
        
        # Close the handler to flush
        handler.close()
        
        # Check that the log file contains the messages
        with open(temp_log_file, 'r') as f:
            content = f.read()
            assert 'Test message 1' in content
            assert 'Test message 2' in content
        
        # Clean up
        logger.removeHandler(handler)


class TestLogRotationIntegration:
    """Integration tests for the complete log rotation system."""
    
    @pytest.fixture
    def temp_project_dir(self):
        """Create a temporary project directory structure."""
        temp_dir = tempfile.mkdtemp()
        project_path = Path(temp_dir)
        logs_dir = project_path / 'logs'
        logs_dir.mkdir()
        
        yield project_path
        shutil.rmtree(temp_dir)
    
    def test_full_log_rotation_setup(self, temp_project_dir):
        """Test the complete log rotation setup process."""
        logs_dir = temp_project_dir / 'logs'
        rotation_manager = LogRotationManager(logs_dir)
        
        # Set up rotation with default configs
        rotation_manager.setup_log_rotation()
        
        # Check that handlers were created
        assert len(rotation_manager.managed_handlers) > 0
        
        # Check that log files can be created
        for log_name, handler in rotation_manager.managed_handlers.items():
            # Create a test logger
            logger = logging.getLogger(f'test_{log_name}')
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
            
            # Log a test message
            logger.info(f'Test message for {log_name}')
            
            # Check that the log file was created
            log_path = Path(handler.baseFilename)
            assert log_path.exists()
            
            # Clean up
            logger.removeHandler(handler)
            handler.close()
    
    def test_log_rotation_with_existing_loggers(self, temp_project_dir):
        """Test that rotation works with existing loggers."""
        logs_dir = temp_project_dir / 'logs'
        rotation_manager = LogRotationManager(logs_dir)
        
        # Create some existing loggers
        existing_loggers = [
            'n8n_builder',
            'self_healer',
            'n8n_builder.validation'
        ]
        
        for logger_name in existing_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.INFO)
        
        # Set up rotation
        rotation_manager.setup_log_rotation()
        
        # Test that we can log to the existing loggers
        for logger_name in existing_loggers:
            logger = logging.getLogger(logger_name)
            logger.info(f'Test message from {logger_name}')
        
        # Check that log files were created
        expected_files = ['n8n_builder.log', 'self_healer.log', 'validation.log']
        for filename in expected_files:
            log_path = logs_dir / filename
            # Note: Files might not exist immediately due to buffering
            # This is normal behavior for logging systems


async def run_log_rotation_tests():
    """Run all log rotation tests."""
    print("🧪 Log Rotation Test Suite")
    print("=" * 50)
    
    # Test the log rotation manager
    print("\n📋 Testing LogRotationManager...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Test initialization
        manager = LogRotationManager(temp_path)
        print("✅ Manager initialization: PASS")
        
        # Test default configs
        configs = manager._get_default_log_configs()
        assert len(configs) >= 5
        print("✅ Default configurations: PASS")
        
        # Test single log setup
        test_config = {
            'filename': 'test.log',
            'level': 'INFO',
            'format': '%(asctime)s - %(message)s'
        }
        manager._setup_single_log_rotation('test', test_config)
        assert 'test' in manager.managed_handlers
        print("✅ Single log rotation setup: PASS")
        
        # Test statistics
        stats = manager.get_log_statistics()
        assert 'test' in stats
        print("✅ Log statistics: PASS")
    
    print("\n📋 Testing DatestampedTimedRotatingFileHandler...")
    
    with tempfile.NamedTemporaryFile(delete=False, suffix='.log') as temp_file:
        try:
            # Test handler creation
            handler = DatestampedTimedRotatingFileHandler(
                temp_file.name,
                when='midnight',
                backupCount=7,
                compress=True
            )
            print("✅ Handler initialization: PASS")
            
            # Test logging
            logger = logging.getLogger('test_handler')
            logger.setLevel(logging.INFO)
            logger.addHandler(handler)
            
            logger.info('Test log message')
            handler.close()
            
            # Check file exists and has content
            if os.path.exists(temp_file.name):
                with open(temp_file.name, 'r') as f:
                    content = f.read()
                    if 'Test log message' in content:
                        print("✅ Handler logging: PASS")
                    else:
                        print("⚠️ Handler logging: Content not found")
            else:
                print("⚠️ Handler logging: File not created")
            
            logger.removeHandler(handler)
            
        finally:
            try:
                os.unlink(temp_file.name)
            except FileNotFoundError:
                pass
    
    print("\n🎉 Log rotation tests completed!")
    return True


if __name__ == "__main__":
    asyncio.run(run_log_rotation_tests())
