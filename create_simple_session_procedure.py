import asyncio
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def create_simple_procedure():
    db_tool = MCPDatabaseTool('knowledgebase')
    
    # Drop existing procedure if it exists
    drop_sql = """
    IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_RecentSessions_P')
        DROP PROCEDURE S_SYS_SelfHealer_RecentSessions_P
    """
    
    try:
        print('Dropping existing procedure...')
        await db_tool.execute_query(drop_sql)
        print('✅ Drop completed')
    except Exception as e:
        print(f'Drop procedure (expected if not exists): {e}')
    
    # Create simple procedure that works with actual data
    create_sql = """
    CREATE PROCEDURE S_SYS_SelfHealer_RecentSessions_P
        @Limit INT = 10
    AS
    BEGIN
        SET NOCOUNT ON;
        
        SELECT TOP (@Limit)
            e.ID as EntityID,
            REPLACE(e.Name, 'Session_', '') as SessionID,
            e.CreateDate,
            'Unknown' as Duration,
            'Unknown' as SuccessRate,
            'Resolved' as Status,
            1 as Success
        FROM REF_Entities e
        WHERE e.Name LIKE 'Session_%'
        ORDER BY e.CreateDate DESC
    END
    """
    
    try:
        print('Creating simplified S_SYS_SelfHealer_RecentSessions_P...')
        await db_tool.execute_query(create_sql)
        print('✅ S_SYS_SelfHealer_RecentSessions_P created successfully!')
        
        # Test the procedure
        print('\nTesting the procedure...')
        result = await db_tool.execute_stored_procedure('S_SYS_SelfHealer_RecentSessions_P', {'Limit': 5})
        print(f'Test result: {result}')
        
    except Exception as e:
        print(f'❌ Error creating procedure: {e}')

if __name__ == "__main__":
    asyncio.run(create_simple_procedure())
