"""
Comprehensive test suite for KnowledgeBase _SYS_ stored procedures.

This script validates all the newly created stored procedures in the KnowledgeBase
database to ensure they are working properly after the database schema updates.

Author: N8N Builder System
Date: 2025-01-01
Purpose: Validate KnowledgeBase stored procedure functionality
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from n8n_builder.mcp_database_tool import MCPDatabaseTool


class KnowledgeBaseProcedureValidator:
    """Comprehensive validator for KnowledgeBase stored procedures."""
    
    def __init__(self, connection_name: str = 'knowledgebase'):
        """Initialize the validator with database connection."""
        self.db_tool = MCPDatabaseTool(connection_name)
        self.connection_name = connection_name
        self.test_results = []
        
    def log_test_result(self, test_name: str, status: str, details: str = "", error: str = ""):
        """Log test result for summary reporting."""
        self.test_results.append({
            'test_name': test_name,
            'status': status,
            'details': details,
            'error': error,
            'timestamp': datetime.now().isoformat()
        })
        
    async def test_database_connection(self) -> bool:
        """Test basic database connectivity."""
        print("🔌 Testing Database Connection...")
        try:
            result = await self.db_tool.execute_query("SELECT @@VERSION as version, DB_NAME() as database_name")
            
            if result['status'] == 'success' and result['rows']:
                db_info = result['rows'][0]
                print(f"✅ Connected to: {db_info['database_name']}")
                print(f"   SQL Server Version: {db_info['version'][:50]}...")
                self.log_test_result("Database Connection", "PASS", f"Connected to {db_info['database_name']}")
                return True
            else:
                print("❌ Connection failed - no data returned")
                self.log_test_result("Database Connection", "FAIL", "", "No data returned from connection test")
                return False
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            self.log_test_result("Database Connection", "FAIL", "", str(e))
            return False
    
    async def test_procedure_existence(self) -> bool:
        """Test that all expected stored procedures exist."""
        print("\n📋 Testing Stored Procedure Existence...")
        
        expected_procedures = [
            'S_SYS_XRF_Entity_Attribute_Value_P_EntityID',
            'S_SYS_REF_Fact_P_ErrorType', 
            'S_SYS_REF_Opinion_P_ErrorType',
            'S_SYS_REF_Fact_SelfHealer_Analytics_Prms',
            'S_SYS_SelfHealer_KnowledgeSearch_Prms'
        ]
        
        try:
            # Check if procedures exist
            query = """
            SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
            """.format(','.join([f"'{proc}'" for proc in expected_procedures]))
            
            result = await self.db_tool.execute_query(query)
            
            if result['status'] != 'success':
                print(f"❌ Query failed: {result}")
                self.log_test_result("Procedure Existence", "FAIL", "", "Query execution failed")
                return False
            
            found_procedures = {row['name']: row for row in result['rows']}
            all_found = True
            
            for proc in expected_procedures:
                if proc in found_procedures:
                    proc_info = found_procedures[proc]
                    print(f"✅ Found: {proc}")
                    print(f"   Created: {proc_info['create_date']}")
                    print(f"   Modified: {proc_info['modify_date']}")
                else:
                    print(f"❌ Missing: {proc}")
                    all_found = False
            
            status = "PASS" if all_found else "FAIL"
            details = f"Found {len(found_procedures)} of {len(expected_procedures)} procedures"
            self.log_test_result("Procedure Existence", status, details)
            
            print(f"\n📊 Summary: {details}")
            return all_found
            
        except Exception as e:
            print(f"❌ Error checking procedures: {e}")
            self.log_test_result("Procedure Existence", "FAIL", "", str(e))
            return False
    
    async def test_entity_attribute_procedure(self) -> bool:
        """Test S_SYS_XRF_Entity_Attribute_Value_P_EntityID procedure."""
        print("\n🔗 Testing Entity Attribute Procedure...")
        
        try:
            # First, check if we have any entities to test with
            entities_query = "SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID"
            entities_result = await self.db_tool.execute_query(entities_query)
            
            if entities_result['status'] != 'success' or not entities_result['rows']:
                print("ℹ️  No entities found in database - creating test entity")
                # Create a test entity for testing
                test_entity_query = """
                INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
                """
                create_result = await self.db_tool.execute_query(test_entity_query)
                if create_result['status'] == 'success' and create_result['rows']:
                    test_entity_id = create_result['rows'][0]['ID']
                    print(f"✅ Created test entity with ID: {test_entity_id}")
                else:
                    print("❌ Failed to create test entity")
                    self.log_test_result("Entity Attribute Procedure", "FAIL", "", "Could not create test entity")
                    return False
            else:
                test_entity_id = entities_result['rows'][0]['ID']
                print(f"ℹ️  Using existing entity ID: {test_entity_id}")
            
            # Test the stored procedure
            result = await self.db_tool.execute_stored_procedure(
                'S_SYS_XRF_Entity_Attribute_Value_P_EntityID',
                {'EntityID': test_entity_id}
            )
            
            if result['status'] == 'success':
                if result.get('result_sets') and result['result_sets'][0]['rows']:
                    attributes = result['result_sets'][0]['rows']
                    print(f"✅ Procedure executed successfully")
                    print(f"   Found {len(attributes)} attributes for entity {test_entity_id}")
                    if attributes:
                        print(f"   Sample attribute: {attributes[0].get('AttributeName', 'N/A')}")
                else:
                    print(f"✅ Procedure executed successfully (no attributes found for entity {test_entity_id})")
                
                self.log_test_result("Entity Attribute Procedure", "PASS", f"Executed for entity {test_entity_id}")
                return True
            else:
                print(f"❌ Procedure failed: {result.get('error', 'Unknown error')}")
                self.log_test_result("Entity Attribute Procedure", "FAIL", "", result.get('error', 'Unknown error'))
                return False
                
        except Exception as e:
            print(f"❌ Error testing entity attribute procedure: {e}")
            self.log_test_result("Entity Attribute Procedure", "FAIL", "", str(e))
            return False
    
    async def test_facts_by_error_type_procedure(self) -> bool:
        """Test S_SYS_REF_Fact_P_ErrorType procedure."""
        print("\n📊 Testing Facts by Error Type Procedure...")
        
        test_error_types = ['Database', 'Connection', 'SSL', 'Authentication', 'Timeout']
        
        try:
            for error_type in test_error_types:
                print(f"\n   Testing with error type: '{error_type}'")
                
                result = await self.db_tool.execute_stored_procedure(
                    'S_SYS_REF_Fact_P_ErrorType',
                    {'ErrorType': error_type}
                )
                
                if result['status'] == 'success':
                    if result.get('result_sets') and result['result_sets'][0]['rows']:
                        facts = result['result_sets'][0]['rows']
                        print(f"   ✅ Found {len(facts)} facts")
                        if facts:
                            sample_fact = facts[0]
                            print(f"      Sample: {sample_fact.get('Name', 'N/A')[:50]}...")
                            print(f"      Validity: {sample_fact.get('ValidityRating', 'N/A')}")
                    else:
                        print(f"   ℹ️  No facts found for '{error_type}'")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                    self.log_test_result("Facts by Error Type", "FAIL", f"Error type: {error_type}", result.get('error'))
                    return False
            
            self.log_test_result("Facts by Error Type", "PASS", f"Tested {len(test_error_types)} error types")
            print(f"\n✅ Facts procedure tested successfully with {len(test_error_types)} error types")
            return True
            
        except Exception as e:
            print(f"❌ Error testing facts procedure: {e}")
            self.log_test_result("Facts by Error Type", "FAIL", "", str(e))
            return False

    async def test_opinions_by_error_type_procedure(self) -> bool:
        """Test S_SYS_REF_Opinion_P_ErrorType procedure."""
        print("\n💭 Testing Opinions by Error Type Procedure...")

        test_error_types = ['Database', 'Connection', 'SSL', 'Authentication', 'Timeout']

        try:
            for error_type in test_error_types:
                print(f"\n   Testing with error type: '{error_type}'")

                result = await self.db_tool.execute_stored_procedure(
                    'S_SYS_REF_Opinion_P_ErrorType',
                    {'ErrorType': error_type}
                )

                if result['status'] == 'success':
                    if result.get('result_sets') and result['result_sets'][0]['rows']:
                        opinions = result['result_sets'][0]['rows']
                        print(f"   ✅ Found {len(opinions)} opinions")
                        if opinions:
                            sample_opinion = opinions[0]
                            print(f"      Sample: {sample_opinion.get('Name', 'N/A')[:50]}...")
                            print(f"      Validity: {sample_opinion.get('ValidityRating', 'N/A')}")
                    else:
                        print(f"   ℹ️  No opinions found for '{error_type}'")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                    self.log_test_result("Opinions by Error Type", "FAIL", f"Error type: {error_type}", result.get('error', 'Unknown error'))
                    return False

            self.log_test_result("Opinions by Error Type", "PASS", f"Tested {len(test_error_types)} error types")
            print(f"\n✅ Opinions procedure tested successfully with {len(test_error_types)} error types")
            return True

        except Exception as e:
            print(f"❌ Error testing opinions procedure: {e}")
            self.log_test_result("Opinions by Error Type", "FAIL", "", str(e))
            return False

    async def test_selfhealer_analytics_procedure(self) -> bool:
        """Test S_SYS_REF_Fact_SelfHealer_Analytics_Prms procedure."""
        print("\n📈 Testing Self-Healer Analytics Procedure...")

        test_scenarios = [
            {'Category': None, 'MinValidity': 0, 'Limit': 10},
            {'Category': 'Database', 'MinValidity': 50.0, 'Limit': 5},
            {'Category': 'Connection', 'MinValidity': 70.0, 'Limit': 3},
        ]

        try:
            for i, params in enumerate(test_scenarios):
                print(f"\n   Test scenario {i+1}: {params}")

                result = await self.db_tool.execute_stored_procedure(
                    'S_SYS_REF_Fact_SelfHealer_Analytics_Prms',
                    params
                )

                if result['status'] == 'success':
                    if result.get('result_sets') and result['result_sets'][0]['rows']:
                        analytics = result['result_sets'][0]['rows']
                        print(f"   ✅ Found {len(analytics)} analytics records")
                        if analytics:
                            sample_record = analytics[0]
                            print(f"      Sample: {sample_record.get('Name', 'N/A')[:50]}...")
                            print(f"      Category: {sample_record.get('Category', 'N/A')}")
                            print(f"      Validity: {sample_record.get('ValidityRating', 'N/A')}")
                    else:
                        print(f"   ℹ️  No analytics found for scenario {i+1}")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                    self.log_test_result("Self-Healer Analytics", "FAIL", f"Scenario {i+1}", result.get('error', 'Unknown error'))
                    return False

            self.log_test_result("Self-Healer Analytics", "PASS", f"Tested {len(test_scenarios)} scenarios")
            print(f"\n✅ Analytics procedure tested successfully with {len(test_scenarios)} scenarios")
            return True

        except Exception as e:
            print(f"❌ Error testing analytics procedure: {e}")
            self.log_test_result("Self-Healer Analytics", "FAIL", "", str(e))
            return False

    async def test_knowledge_search_procedure(self) -> bool:
        """Test S_SYS_SelfHealer_KnowledgeSearch_Prms procedure."""
        print("\n🔍 Testing Knowledge Search Procedure...")

        test_scenarios = [
            {'SearchQuery': 'error', 'KnowledgeType': None, 'MinValidity': 0, 'Limit': 5},
            {'SearchQuery': 'database', 'KnowledgeType': 'fact', 'MinValidity': 50.0, 'Limit': 3},
            {'SearchQuery': 'connection', 'KnowledgeType': 'opinion', 'MinValidity': 60.0, 'Limit': 3},
            {'SearchQuery': 'SSL', 'KnowledgeType': None, 'MinValidity': 70.0, 'Limit': 10},
        ]

        try:
            for i, params in enumerate(test_scenarios):
                print(f"\n   Test scenario {i+1}: {params}")

                result = await self.db_tool.execute_stored_procedure(
                    'S_SYS_SelfHealer_KnowledgeSearch_Prms',
                    params
                )

                if result['status'] == 'success':
                    total_results = 0
                    if result.get('result_sets'):
                        for j, result_set in enumerate(result['result_sets']):
                            if result_set['rows']:
                                count = len(result_set['rows'])
                                total_results += count
                                print(f"      Result set {j+1}: {count} records")

                                # Show sample from first result set
                                if j == 0 and result_set['rows']:
                                    sample = result_set['rows'][0]
                                    print(f"         Sample: {sample.get('Name', 'N/A')[:40]}...")

                    if total_results > 0:
                        print(f"   ✅ Found {total_results} total search results")
                    else:
                        print(f"   ℹ️  No search results found for scenario {i+1}")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
                    self.log_test_result("Knowledge Search", "FAIL", f"Scenario {i+1}", result.get('error', 'Unknown error'))
                    return False

            self.log_test_result("Knowledge Search", "PASS", f"Tested {len(test_scenarios)} search scenarios")
            print(f"\n✅ Knowledge search procedure tested successfully with {len(test_scenarios)} scenarios")
            return True

        except Exception as e:
            print(f"❌ Error testing knowledge search procedure: {e}")
            self.log_test_result("Knowledge Search", "FAIL", "", str(e))
            return False

    async def test_database_schema_access(self) -> bool:
        """Test access to database schema information."""
        print("\n🗄️  Testing Database Schema Access...")

        try:
            # Test the schema procedure if it exists
            schema_query = """
            SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
            """

            schema_check = await self.db_tool.execute_query(schema_query)

            if schema_check['status'] == 'success' and schema_check['rows']:
                print("✅ Schema procedure exists - testing execution")

                # Test the schema procedure
                schema_result = await self.db_tool.execute_stored_procedure(
                    'z_S_SYS_Admin_KnowledgeBaseSchema',
                    {}
                )

                if schema_result['status'] == 'success':
                    print("✅ Schema procedure executed successfully")
                    if schema_result.get('result_sets'):
                        print(f"   Returned {len(schema_result['result_sets'])} result sets")
                        for i, rs in enumerate(schema_result['result_sets']):
                            print(f"   Result set {i+1}: {len(rs['rows'])} rows")

                    self.log_test_result("Database Schema Access", "PASS", "Schema procedure executed")
                    return True
                else:
                    print(f"❌ Schema procedure failed: {schema_result.get('error', 'Unknown error')}")
                    self.log_test_result("Database Schema Access", "FAIL", "", schema_result.get('error', 'Unknown error'))
                    return False
            else:
                print("ℹ️  Schema procedure not found - testing basic table access")

                # Test basic table access
                tables_query = """
                SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
                """

                tables_result = await self.db_tool.execute_query(tables_query)

                if tables_result['status'] == 'success':
                    table_count = len(tables_result['rows'])
                    print(f"✅ Found {table_count} REF_ tables")
                    if tables_result['rows']:
                        print("   Sample tables:")
                        for table in tables_result['rows'][:3]:
                            print(f"     - {table['TABLE_NAME']}")

                    self.log_test_result("Database Schema Access", "PASS", f"Found {table_count} tables")
                    return True
                else:
                    print(f"❌ Table access failed: {tables_result.get('error', 'Unknown error')}")
                    self.log_test_result("Database Schema Access", "FAIL", "", tables_result.get('error', 'Unknown error'))
                    return False

        except Exception as e:
            print(f"❌ Error testing schema access: {e}")
            self.log_test_result("Database Schema Access", "FAIL", "", str(e))
            return False

    def print_test_summary(self):
        """Print a comprehensive test summary."""
        print("\n" + "="*80)
        print("🧪 KNOWLEDGEBASE STORED PROCEDURES TEST SUMMARY")
        print("="*80)

        passed_tests = [t for t in self.test_results if t['status'] == 'PASS']
        failed_tests = [t for t in self.test_results if t['status'] == 'FAIL']

        print(f"\n📊 Overall Results:")
        print(f"   ✅ Passed: {len(passed_tests)}")
        print(f"   ❌ Failed: {len(failed_tests)}")
        print(f"   📈 Success Rate: {len(passed_tests)/(len(self.test_results)) * 100:.1f}%")

        if passed_tests:
            print(f"\n✅ Successful Tests:")
            for test in passed_tests:
                print(f"   • {test['test_name']}: {test['details']}")

        if failed_tests:
            print(f"\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"   • {test['test_name']}: {test['error']}")
                if test['details']:
                    print(f"     Details: {test['details']}")

        print(f"\n🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)

        return len(failed_tests) == 0

    async def run_all_tests(self) -> bool:
        """Run all stored procedure validation tests."""
        print("🧪 KNOWLEDGEBASE STORED PROCEDURES VALIDATION")
        print("="*60)
        print(f"Connection: {self.connection_name}")
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)

        # Run all tests in sequence
        tests = [
            self.test_database_connection(),
            self.test_procedure_existence(),
            self.test_entity_attribute_procedure(),
            self.test_facts_by_error_type_procedure(),
            self.test_opinions_by_error_type_procedure(),
            self.test_selfhealer_analytics_procedure(),
            self.test_knowledge_search_procedure(),
            self.test_database_schema_access(),
        ]

        all_passed = True
        for test in tests:
            try:
                result = await test
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"❌ Test execution error: {e}")
                all_passed = False

        # Print summary
        success = self.print_test_summary()

        if success and all_passed:
            print("\n🎉 All tests passed! KnowledgeBase stored procedures are working correctly.")
            return True
        else:
            print("\n⚠️  Some tests failed. Please review the errors above.")
            return False


async def main():
    """Main execution function."""
    try:
        validator = KnowledgeBaseProcedureValidator('knowledgebase')
        success = await validator.run_all_tests()

        if success:
            print("\n✅ Validation completed successfully!")
            return 0
        else:
            print("\n❌ Validation completed with errors!")
            return 1

    except Exception as e:
        print(f"\n💥 Critical error during validation: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
