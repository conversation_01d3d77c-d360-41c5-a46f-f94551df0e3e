@echo off
echo ========================================
echo N8N_Builder Commit Message Cleanup
echo ========================================
echo.

echo Creating backup branch...
git branch backup-before-cleanup-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%
if errorlevel 1 (
    echo ERROR: Failed to create backup branch
    pause
    exit /b 1
)
echo Backup branch created successfully.
echo.

echo WARNING: This will rewrite Git history!
echo Make sure you have backups before proceeding.
echo.
set /p confirm="Do you want to proceed? (y/N): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting commit message rewrite...
echo This may take several minutes...
echo.

REM Set environment variable to suppress filter-branch warning
set FILTER_BRANCH_SQUELCH_WARNING=1

REM Create a simple sed-like replacement using PowerShell
echo Creating message filter...

REM Use git filter-branch with a simple replacement approach
git filter-branch -f --msg-filter "powershell -Command \"$input = $args[0]; switch ($input) { 'Remove additional private Self-Healer and KnowledgeBase development files' { 'Remove additional private development files' }; 'Enhanced .gitignore to comprehensively exclude all private Self-Healer and KnowledgeBase components' { 'Enhanced .gitignore to exclude private components' }; 'Remove private Self-Healer and KnowledgeBase components from public repository' { 'Remove private components from public repository' }; 'update gitignore for self-healer and knowledgebase' { 'update gitignore for private components' }; 'Updates for Self-Healer Separation Finalization' { 'Updates for system separation finalization' }; 'Updates to KnowledgeBase Table structures and SP' { 'Updates to database table structures and SP' }; 'Updates to Self-Healer' { 'Updates to system components' }; 'Self-Healder - KnowledgeBase Fix' { 'System component fixes' }; 'Self-Healer & KnowledgeBase Updates 4' { 'System component updates 4' }; 'Self-Healer & KnowledgeBase Updates 3' { 'System component updates 3' }; 'Self-Healer & KnowledgeBse Integration Updates 2' { 'System integration updates 2' }; 'Self-Healer & KnowledgeBase Integration Updates' { 'System integration updates' }; 'KnowledgeBase Updates' { 'Database component updates' }; 'Update to Full version of Self-Healer' { 'Update to full system version' }; 'Fixes to Self-Healer and Documentation' { 'Fixes to system and documentation' }; 'Updates to Self-Healer / KnowledgeBase' { 'Updates to system components' }; 'Self-Healer Finalized and Integrated with KnowledgeBase - First Pass' { 'System integration finalized - first pass' }; 'Self Healer Updates' { 'System component updates' }; 'Self-Healer' { 'System component implementation' }; default { $input } }\"" -- --all

if errorlevel 1 (
    echo ERROR: Git filter-branch failed
    echo You can restore using the backup branch
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS: Commit messages cleaned up!
echo ========================================
echo.
echo Next steps:
echo 1. Review changes: git log --oneline
echo 2. Force push: git push --force-with-lease
echo 3. If issues: restore from backup branch
echo.
pause
