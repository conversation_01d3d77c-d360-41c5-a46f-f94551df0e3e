import asyncio
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def check_schema():
    db_tool = MCPDatabaseTool('knowledgebase')
    
    # Get all tables
    print("=== All Tables ===")
    tables_query = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
    result = await db_tool.execute_query(tables_query)
    
    for row in result['rows']:
        table_name = row['TABLE_NAME']
        print(f"Table: {table_name}")
    
    print("\n=== REF_Entity with Session data ===")
    entities_query = "SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'"
    entities = await db_tool.execute_query(entities_query)
    print(f"Result keys: {entities.keys()}")
    if 'rows' in entities:
        print(f"Session entities found: {len(entities['rows'])}")
        for row in entities['rows']:
            print(f"  {row}")

    print("\n=== All REF_Entities ===")
    all_entities = await db_tool.execute_query("SELECT TOP 10 * FROM REF_Entities ORDER BY CreateDate DESC")
    if 'rows' in all_entities:
        print(f"Total entities: {len(all_entities['rows'])}")
        for row in all_entities['rows']:
            print(f"  {row}")

    print("\n=== Check XRF_EntityAttributeValue ===")
    xrf_query = "SELECT TOP 5 * FROM XRF_EntityAttributeValue"
    xrf_result = await db_tool.execute_query(xrf_query)
    if 'rows' in xrf_result:
        print(f"XRF records: {len(xrf_result['rows'])}")
        for row in xrf_result['rows']:
            print(f"  {row}")

if __name__ == "__main__":
    asyncio.run(check_schema())
