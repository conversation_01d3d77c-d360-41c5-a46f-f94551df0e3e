# 📚 N8N_Builder Documentation

**🎯 AI-powered workflow automation made simple - Complete documentation and guides**

## 🚀 Quick Start

| Time Available | Start Here |
|----------------|------------|
| **2 minutes** | [⚡ Lightning Start](../LIGHTNING_START.md) |
| **15 minutes** | [📖 Getting Started](../GETTING_STARTED.md) |

## 📋 Common Tasks

| I Want To... | Guide |
|--------------|-------|
| **Get it working NOW** | [⚡ Lightning Start](../LIGHTNING_START.md) |
| **Understand the system** | [📖 Getting Started](../GETTING_STARTED.md) |
| **Fix problems** | [🔧 Troubleshooting](TROUBLESHOOTING.md) |
| **Use the API** | [📚 API Documentation](api/API_DOCUMENTATION.md) |
| **Deploy to production** | [🐳 n8n-docker Guide](../n8n-docker/Documentation/README.md) |

## 🏗️ How It Works

**Simple:** Describe your automation in plain English → AI generates n8n workflow → Import and run

## 🎯 What You Can Build

- *"Send me an email when a new file is uploaded"*
- *"Post to Twitter when I publish a blog article"*  
- *"Convert CSV files to JSON and send to webhook"*
- *"Alert me when my website goes down"*

## 📚 Documentation Sections

### 🔧 **For Users**
- [🔧 Troubleshooting](TROUBLESHOOTING.md) - Fix common problems
- [🐳 n8n-docker Setup](../n8n-docker/Documentation/README.md) - Production environment
- [🐳 Docker Installation Guide](../n8n-docker/Documentation/technical/DOCKER_SETUP.md) - Complete Docker setup

### 🔌 **For Developers**  
- [📚 API Documentation](api/API_DOCUMENTATION.md) - Complete API reference
- [⚡ API Quick Reference](api/API_QUICK_REFERENCE.md) - Common examples
- [🏗️ Technical Architecture](technical/DOCUMENTATION.md) - System design

### 🤖 **Advanced Features**
- [🔧 Automated System](ADVANCED_FEATURES.md) - Automatic error resolution
- [🗄️ Data Management](DATABASE_INTEGRATION.md) - Database integration

## 🆘 Need Help?

- **🔧 [Troubleshooting Guide](TROUBLESHOOTING.md)** - Fix common issues
- **🐛 [GitHub Issues](https://github.com/vbwyrde/N8N_Builder/issues)** - Report bugs

---

**🎉 Ready to start?** Begin with [⚡ Lightning Start](../LIGHTNING_START.md) for a 2-minute setup!
