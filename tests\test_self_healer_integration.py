#!/usr/bin/env python3
"""
Test script to verify Self-Healer integration with N8N Builder.
This script simulates errors and verifies that Self-Healer detects and processes them.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add Self-Healer to path
sys.path.append(str(Path(__file__).parent / 'Self-Healer'))

from n8n_builder.error_handler import EnhancedError<PERSON>andler, ErrorDetail, ErrorCategory, ErrorSeverity

# Configure logging to write to the files Self-Healer monitors
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/errors.log'),
        logging.FileHandler('logs/n8n_builder.log')
    ]
)

logger = logging.getLogger('self_healer_test')

async def test_self_healer_integration():
    """Test Self-Healer integration by simulating various error scenarios."""
    
    print("=" * 60)
    print("Self-Healer Integration Test")
    print("=" * 60)
    print()
    
    # Test 1: Check if Self-Healer can be imported
    print("Test 1: Self-Healer Import Test")
    try:
        from core.healer_manager import SelfHealerManager
        print("✓ Self-Healer successfully imported")
        healer_available = True
    except ImportError as e:
        print(f"✗ Self-Healer import failed: {e}")
        print("  Make sure Self-Healer is properly installed")
        healer_available = False
    print()
    
    if not healer_available:
        print("Cannot proceed with integration tests - Self-Healer not available")
        return
    
    # Test 2: Initialize Self-Healer
    print("Test 2: Self-Healer Initialization")
    try:
        healer = SelfHealerManager()
        await healer.start()
        print("✓ Self-Healer Manager initialized and started")
        
        # Wait a moment for initialization
        await asyncio.sleep(2)
        
        status = await healer.get_status()
        print(f"  Status: {status['status']}")
        print(f"  Is Running: {status['is_running']}")
        print()
        
    except Exception as e:
        print(f"✗ Self-Healer initialization failed: {e}")
        return
    
    # Test 3: Simulate LLM Connection Error
    print("Test 3: Simulated LLM Connection Error")
    try:
        # Create a mock LLM connection error
        error_handler = EnhancedErrorHandler()
        connection_error = ConnectionError("Connection refused: localhost:1234")
        
        # Log the error (this should be detected by Self-Healer)
        logger.error(f"LLM Connection Failed: {connection_error}")
        
        # Categorize the error using existing error handler
        error_detail = error_handler.categorize_error(connection_error, {
            'operation_id': 'test_llm_connection',
            'endpoint': 'localhost:1234'
        })
        
        print(f"  Error Category: {error_detail.category}")
        print(f"  Error Title: {error_detail.title}")
        print(f"  Error Message: {error_detail.message}")
        print("  ✓ Error logged - Self-Healer should detect this")
        
    except Exception as e:
        print(f"✗ Error simulation failed: {e}")
    print()
    
    # Test 4: Wait for Self-Healer to detect and process errors
    print("Test 4: Monitoring Self-Healer Response")
    print("  Waiting 10 seconds for Self-Healer to detect and process errors...")
    
    for i in range(10):
        await asyncio.sleep(1)
        
        # Check for new healing sessions
        status = await healer.get_status()
        active_sessions = status.get('active_sessions', 0)
        total_errors = status.get('metrics', {}).get('total_errors_detected', 0)
        
        if i % 3 == 0:  # Update every 3 seconds
            print(f"    [{i+1}/10] Active Sessions: {active_sessions}, Total Errors: {total_errors}")
    
    # Final status check
    final_status = await healer.get_status()
    print(f"  Final Status:")
    print(f"    Total Errors Detected: {final_status['metrics']['total_errors_detected']}")
    print(f"    Total Healing Attempts: {final_status['metrics']['total_healing_attempts']}")
    print(f"    Success Rate: {final_status['metrics']['success_rate']:.1f}%")
    print()
    
    # Test 5: Test Dashboard Availability
    print("Test 5: Dashboard Integration Test")
    try:
        from dashboard.dashboard import SelfHealerDashboard
        dashboard = SelfHealerDashboard(healer, port=8082)  # Use different port for test
        print("✓ Dashboard can be initialized")
        print("  Dashboard would be available at: http://localhost:8082")
        print("  (Not starting dashboard in test mode)")
    except Exception as e:
        print(f"✗ Dashboard initialization failed: {e}")
    print()
    
    # Test 6: Manual Healing Test
    print("Test 6: Manual Healing Test")
    try:
        # Create a test error for manual healing
        test_error = ErrorDetail(
            category=ErrorCategory.JSON_PARSING,
            severity=ErrorSeverity.ERROR,
            title="Test JSON Parsing Error",
            message="Simulated JSON parsing error for testing",
            user_guidance="This is a test error",
            technical_details="JSONDecodeError: Test error",
            context={'error_id': 'test_manual_001', 'test_mode': True}
        )
        
        # Analyze error context
        context = await healer.context_analyzer.analyze_error(test_error)
        print(f"  ✓ Context analysis completed")
        print(f"    Related files: {len(context.get('related_files', []))}")
        print(f"    Relevant docs: {len(context.get('relevant_docs', []))}")
        
        # Generate solutions
        solutions = await healer.solution_generator.generate_solutions(test_error, context)
        print(f"  ✓ Solution generation completed")
        print(f"    Generated {len(solutions)} solutions")
        
        if solutions:
            best_solution = solutions[0]
            print(f"    Best solution: {best_solution.get('title', 'Unknown')}")
            print(f"    Confidence: {best_solution.get('confidence', 0):.2f}")
        
    except Exception as e:
        print(f"✗ Manual healing test failed: {e}")
    print()
    
    # Cleanup
    print("Test Cleanup")
    try:
        await healer.stop()
        print("✓ Self-Healer stopped successfully")
    except Exception as e:
        print(f"✗ Error stopping Self-Healer: {e}")
    
    print()
    print("=" * 60)
    print("Integration Test Complete")
    print("=" * 60)
    print()
    print("Next Steps:")
    print("1. Run 'python run.py' to start N8N Builder with Self-Healer")
    print("2. Access the main application at: http://localhost:8002")
    print("3. Access the Self-Healer dashboard at: http://localhost:8081")
    print("4. Generate workflows and observe automatic error healing")

if __name__ == "__main__":
    asyncio.run(test_self_healer_integration())
