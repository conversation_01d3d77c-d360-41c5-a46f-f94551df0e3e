#!/usr/bin/env python3
"""
Debug script to understand why errors aren't meeting healing criteria.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging to see debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def debug_error_criteria():
    """Debug why errors aren't meeting healing criteria."""
    
    print("=" * 60)
    print("DEBUGGING ERROR CRITERIA")
    print("=" * 60)
    
    try:
        from Self_Healer.core.error_monitor import ErrorMonitor
        
        # Initialize error monitor
        error_monitor = ErrorMonitor()
        await error_monitor.start()
        print(f"✅ ErrorMonitor started")
        
        # Force rescan
        print(f"\n🔍 Forcing rescan...")
        new_errors_found = await error_monitor.force_rescan_logs(hours_back=2)
        print(f"Found {new_errors_found} errors during rescan")
        print(f"Total errors in memory: {len(error_monitor.detected_errors)}")
        
        # Show first few errors
        print(f"\n📋 First 3 detected errors:")
        for i, (error_id, detected_error) in enumerate(list(error_monitor.detected_errors.items())[:3]):
            print(f"  {i+1}. ID: {error_id[:8]}")
            print(f"     Title: {detected_error.error_detail.title}")
            print(f"     Category: {detected_error.category}")
            print(f"     Severity: {detected_error.severity}")
            print(f"     Timestamp: {detected_error.timestamp}")
            print(f"     Message: {detected_error.error_detail.message[:80]}...")
            print()
        
        # Test get_new_errors with detailed logging
        print(f"🎯 Testing get_new_errors criteria...")
        new_errors = await error_monitor.get_new_errors()
        print(f"Errors meeting healing criteria: {len(new_errors)}")
        
        if new_errors:
            print(f"\n✅ Errors ready for healing:")
            for error in new_errors:
                print(f"  - {error.title}: {error.message[:60]}...")
        else:
            print(f"\n❌ No errors meet healing criteria")
            
        await error_monitor.stop()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_error_criteria())
