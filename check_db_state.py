#!/usr/bin/env python3
"""
Quick database state check for SQL script validation
"""

import asyncio
import sys
import os
sys.path.insert(0, '.')
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def check_database_state():
    """Check current database state for SQL script validation."""
    db_tool = MCPDatabaseTool('knowledgebase')
    
    print('=== DATABASE STATE VALIDATION ===')
    
    # 1. Test connection
    print('\n1. Testing database connection:')
    try:
        conn_result = await db_tool.test_connection()
        if conn_result['connected']:
            print(f'  ✅ Connected to: {conn_result["database_name"]}')
        else:
            print(f'  ❌ Connection failed: {conn_result["error"]}')
            return
    except Exception as e:
        print(f'  ❌ Connection error: {e}')
        return
    
    # 2. Check REF_EntityValues table structure
    print('\n2. Checking REF_EntityValues table:')
    try:
        columns_query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValues'
        ORDER BY ORDINAL_POSITION
        """
        columns_result = await db_tool.execute_query(columns_query)
        
        if columns_result.get('rows'):
            print(f'  ✅ Table exists with {len(columns_result["rows"])} columns:')
            has_numeric_value = False
            has_value_units = False
            for col in columns_result['rows']:
                col_name = col['COLUMN_NAME']
                col_type = col['DATA_TYPE']
                nullable = col['IS_NULLABLE']
                print(f'    - {col_name}: {col_type} (Nullable: {nullable})')
                if col_name == 'NumericValue':
                    has_numeric_value = True
                if col_name == 'ValueUnits':
                    has_value_units = True
            
            print(f'  📊 NumericValue column exists: {has_numeric_value}')
            print(f'  📊 ValueUnits column exists: {has_value_units}')
        else:
            print('  ❌ REF_EntityValues table not found!')
    except Exception as e:
        print(f'  ❌ Error checking table: {e}')
    
    # 3. Check stored procedures
    print('\n3. Checking stored procedures:')
    try:
        procs_query = """
        SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
        """
        procs_result = await db_tool.execute_query(procs_query)
        
        if procs_result.get('rows'):
            print(f'  ✅ Found {len(procs_result["rows"])} relevant procedures:')
            for proc in procs_result['rows']:
                print(f'    - {proc["name"]} (created: {proc["create_date"]})')
        else:
            print('  ❌ No Self-Healer procedures found!')
    except Exception as e:
        print(f'  ❌ Error checking procedures: {e}')
    
    # 4. Check specific key procedures
    print('\n4. Checking key procedures:')
    key_procedures = [
        'z_S_SYS_Admin_KnowledgeBaseSchema',
        'S_SYS_SelfHealer_RecentSessions_P',
        'S_SYS_SelfHealer_SessionAnalytics_P'
    ]
    
    for proc_name in key_procedures:
        try:
            check_query = f"SELECT name FROM sys.procedures WHERE name = '{proc_name}'"
            check_result = await db_tool.execute_query(check_query)
            
            if check_result.get('rows'):
                print(f'  ✅ {proc_name}: EXISTS')
            else:
                print(f'  ❌ {proc_name}: MISSING')
        except Exception as e:
            print(f'  ❌ {proc_name}: ERROR - {e}')
    
    # 5. Check table counts
    print('\n5. Checking table row counts:')
    try:
        tables_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
        """
        tables_result = await db_tool.execute_query(tables_query)
        
        if tables_result.get('rows'):
            print(f'  ✅ Found {len(tables_result["rows"])} REF tables:')
            for table in tables_result['rows'][:5]:  # Show first 5
                table_name = table['TABLE_NAME']
                try:
                    count_query = f"SELECT COUNT(*) as row_count FROM {table_name}"
                    count_result = await db_tool.execute_query(count_query)
                    if count_result.get('rows'):
                        row_count = count_result['rows'][0]['row_count']
                        print(f'    - {table_name}: {row_count} rows')
                except Exception as e:
                    print(f'    - {table_name}: ERROR - {e}')
        else:
            print('  ❌ No REF tables found!')
    except Exception as e:
        print(f'  ❌ Error checking tables: {e}')
    
    print('\n=== VALIDATION COMPLETE ===')

if __name__ == "__main__":
    asyncio.run(check_database_state())
