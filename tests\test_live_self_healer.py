#!/usr/bin/env python3
"""
Test script to check if the live Self-Healer system is detecting and processing errors.
This connects to the running system to check its actual state.
"""

import asyncio
import sys
import requests
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

async def test_live_self_healer():
    print("=== TESTING LIVE SELF-HEALER SYSTEM ===")
    
    # Test if the Self-Healer dashboard is running
    try:
        print("\n1. TESTING DASHBOARD CONNECTION:")
        dashboard_response = requests.get("http://127.0.0.1:8081/api/status", timeout=5)
        if dashboard_response.status_code == 200:
            status_data = dashboard_response.json()
            print(f"   ✅ Dashboard is running: {status_data}")
        else:
            print(f"   ❌ Dashboard returned status {dashboard_response.status_code}")
    except Exception as e:
        print(f"   ❌ Dashboard connection failed: {e}")
        return
    
    # Test if N8N Builder is running
    try:
        print("\n2. TESTING N8N BUILDER CONNECTION:")
        n8n_response = requests.get("http://127.0.0.1:8002/health", timeout=5)
        if n8n_response.status_code == 200:
            print(f"   ✅ N8N Builder is running")
        else:
            print(f"   ❌ N8N Builder returned status {n8n_response.status_code}")
    except Exception as e:
        print(f"   ❌ N8N Builder connection failed: {e}")
    
    # Check recent errors in the error log
    print("\n3. CHECKING RECENT ERRORS:")
    error_log_path = Path("logs/errors.log")
    if error_log_path.exists():
        with open(error_log_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        # Look for recent errors (last 20 lines)
        recent_lines = lines[-20:]
        recent_errors = []
        
        for line in recent_lines:
            if "ERROR" in line and "15:07:27" in line:  # The error we just generated
                recent_errors.append(line.strip())
        
        print(f"   - Total lines in error log: {len(lines)}")
        print(f"   - Recent errors found: {len(recent_errors)}")
        
        if recent_errors:
            print("   - Recent error details:")
            for error in recent_errors:
                print(f"     * {error}")
        else:
            print("   - No recent errors found in expected timeframe")
    
    # Check if KnowledgeBase has new sessions
    print("\n4. CHECKING KNOWLEDGEBASE FOR NEW SESSIONS:")
    try:
        from n8n_builder.mcp_database_tool import MCPDatabaseTool
        
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Get recent sessions
        session_result = await db_tool.execute_stored_procedure(
            'S_SYS_SelfHealer_RecentSessions_P',
            {'Limit': 5}
        )
        
        sessions = []
        if session_result.get('result_sets'):
            for result_set in session_result['result_sets']:
                if result_set.get('rows'):
                    for row in result_set['rows']:
                        session_time = row.get('CreateDate', '')
                        # Check if session was created in the last 10 minutes
                        if '15:0' in session_time:  # Recent session
                            sessions.append({
                                'id': row.get('SessionID', 'Unknown'),
                                'status': row.get('Status', 'Unknown'),
                                'time': session_time
                            })
        
        print(f"   - Total recent sessions: {len(sessions)}")
        if sessions:
            print("   - Recent session details:")
            for session in sessions:
                print(f"     * {session['id']}: {session['status']} at {session['time']}")
        else:
            print("   - No recent sessions found")
            print("   - This suggests Self-Healer is not processing the new error")
    
    except Exception as e:
        print(f"   ❌ Error checking KnowledgeBase: {e}")
    
    # Force trigger a test error to see if Self-Healer responds
    print("\n5. TESTING ERROR DETECTION WITH NEW ERROR:")
    try:
        # Generate a test error by calling the N8N Builder with invalid input
        test_response = requests.post(
            "http://127.0.0.1:8002/generate",
            json={"description": "invalid test to trigger error"},
            timeout=10
        )
        
        print(f"   - Test request status: {test_response.status_code}")
        
        # Wait a moment for error processing
        await asyncio.sleep(3)
        
        # Check if new session was created
        session_result = await db_tool.execute_stored_procedure(
            'S_SYS_SelfHealer_RecentSessions_P',
            {'Limit': 1}
        )
        
        if session_result.get('result_sets') and session_result['result_sets'][0].get('rows'):
            latest_session = session_result['result_sets'][0]['rows'][0]
            session_time = latest_session.get('CreateDate', '')
            current_time = datetime.now().strftime('%H:%M')
            
            if current_time[:4] in session_time:  # Check if session is very recent
                print(f"   ✅ NEW SESSION CREATED: {latest_session.get('SessionID', 'Unknown')}")
                print(f"      Status: {latest_session.get('Status', 'Unknown')}")
                print(f"      Time: {session_time}")
                print("   🎉 SELF-HEALER IS WORKING!")
            else:
                print(f"   ❌ No new session created (latest: {session_time})")
                print("   🔍 SELF-HEALER IS NOT PROCESSING ERRORS")
        else:
            print("   ❌ No sessions found in KnowledgeBase")
            print("   🔍 SELF-HEALER IS NOT PROCESSING ERRORS")
    
    except Exception as e:
        print(f"   ❌ Error during test: {e}")
    
    print("\n6. SUMMARY:")
    print("   - Dashboard: Running ✅")
    print("   - N8N Builder: Running ✅") 
    print("   - Error Log: Contains recent errors ✅")
    print("   - Self-Healer Processing: Needs investigation 🔍")
    
    print("\n🔍 NEXT STEPS:")
    print("   1. Check if Self-Healer monitoring loop is actually running")
    print("   2. Verify error detection criteria are being met")
    print("   3. Check if errors are being filtered out before processing")

if __name__ == "__main__":
    asyncio.run(test_live_self_healer())
