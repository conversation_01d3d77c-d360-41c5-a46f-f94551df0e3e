"""
Test MCP Database Tool for SQL Server KnowledgeBase connection.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from n8n_builder.mcp_database_tool import MCPDatabaseTool, get_mcp_database
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_database_connection():
    """Test basic database connection."""
    print("🔌 Testing SQL Server KnowledgeBase Connection...")
    print("=" * 60)
    
    try:
        # Create database tool instance
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Test connection
        connection_result = await db_tool.test_connection()
        
        if connection_result['connected']:
            print("✅ Database Connection: SUCCESS")
            print(f"   Server Version: {connection_result['server_version'][:50]}...")
            print(f"   Database Name: {connection_result['database_name']}")
            print(f"   Connection Time: {connection_result['connection_time']}")
            return True
        else:
            print("❌ Database Connection: FAILED")
            print(f"   Error: {connection_result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Database Connection Test Failed: {e}")
        return False


async def test_database_schema():
    """Test database schema retrieval."""
    print("\n📊 Testing Database Schema Retrieval...")
    print("=" * 60)
    
    try:
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Get database schema
        schema = await db_tool.get_database_schema()
        
        print(f"✅ Database Schema Retrieved: {schema.database_name}")
        print(f"   Tables: {len(schema.tables)}")
        print(f"   Views: {len(schema.views)}")
        print(f"   Procedures: {len(schema.procedures)}")
        print(f"   Functions: {len(schema.functions)}")
        
        # Show first few tables
        if schema.tables:
            print("\n📋 Sample Tables:")
            for i, table in enumerate(schema.tables[:5]):
                print(f"   {i+1}. {table['schema']}.{table['name']} ({table['type']})")
            if len(schema.tables) > 5:
                print(f"   ... and {len(schema.tables) - 5} more tables")
        
        # Show first few views
        if schema.views:
            print("\n👁️ Sample Views:")
            for i, view in enumerate(schema.views[:3]):
                print(f"   {i+1}. {view['schema']}.{view['name']}")
            if len(schema.views) > 3:
                print(f"   ... and {len(schema.views) - 3} more views")
        
        return schema
        
    except Exception as e:
        print(f"❌ Database Schema Test Failed: {e}")
        return None


async def test_table_analysis(schema):
    """Test detailed table analysis."""
    print("\n🔍 Testing Table Analysis...")
    print("=" * 60)
    
    if not schema or not schema.tables:
        print("❌ No tables available for analysis")
        return
    
    try:
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Analyze first table
        first_table = schema.tables[0]
        table_name = first_table['name']
        schema_name = first_table['schema']
        
        print(f"🔍 Analyzing Table: {schema_name}.{table_name}")
        
        # Get detailed table info
        table_info = await db_tool.get_table_info(table_name, schema_name)
        
        print(f"✅ Table Analysis Complete:")
        print(f"   Columns: {len(table_info.columns)}")
        print(f"   Indexes: {len(table_info.indexes)}")
        print(f"   Foreign Keys: {len(table_info.foreign_keys)}")
        print(f"   Row Count: {table_info.row_count}")
        
        # Show column details
        if table_info.columns:
            print(f"\n📋 Columns in {table_name}:")
            for col in table_info.columns[:5]:
                nullable = "NULL" if col['is_nullable'] else "NOT NULL"
                print(f"   {col['name']}: {col['data_type']} {nullable}")
            if len(table_info.columns) > 5:
                print(f"   ... and {len(table_info.columns) - 5} more columns")
        
        # Show indexes
        if table_info.indexes:
            print(f"\n🔑 Indexes on {table_name}:")
            for idx in table_info.indexes[:3]:
                unique = "UNIQUE" if idx['is_unique'] else ""
                pk = "PRIMARY KEY" if idx['is_primary_key'] else ""
                print(f"   {idx['name']}: {idx['type']} {unique} {pk}")
                print(f"      Columns: {idx['columns']}")
        
        return table_info
        
    except Exception as e:
        print(f"❌ Table Analysis Failed: {e}")
        return None


async def test_sample_queries(schema):
    """Test sample database queries."""
    print("\n📝 Testing Sample Queries...")
    print("=" * 60)
    
    if not schema or not schema.tables:
        print("❌ No tables available for queries")
        return
    
    try:
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Test basic query
        first_table = schema.tables[0]
        table_name = first_table['name']
        schema_name = first_table['schema']
        
        print(f"🔍 Querying Table: {schema_name}.{table_name}")
        
        # Get sample data
        sample_result = await db_tool.get_sample_data(table_name, schema_name, limit=3)
        
        if sample_result['status'] == 'success':
            print(f"✅ Sample Query Success:")
            print(f"   Columns: {len(sample_result['columns'])}")
            print(f"   Sample Rows: {sample_result['row_count']}")
            
            # Show column names
            if sample_result['columns']:
                print(f"   Column Names: {', '.join(sample_result['columns'][:5])}")
                if len(sample_result['columns']) > 5:
                    print(f"   ... and {len(sample_result['columns']) - 5} more columns")
        else:
            print(f"❌ Sample Query Failed: {sample_result['error']}")
        
        # Test custom query
        print(f"\n🔍 Testing Custom Query...")
        custom_query = f"SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]"
        custom_result = await db_tool.execute_query(custom_query)
        
        if custom_result['status'] == 'success':
            print(f"✅ Custom Query Success:")
            if custom_result['rows']:
                total_rows = custom_result['rows'][0]['total_rows']
                print(f"   Total rows in {table_name}: {total_rows}")
        else:
            print(f"❌ Custom Query Failed: {custom_result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Sample Queries Failed: {e}")
        return False


async def evaluate_for_self_healer():
    """Evaluate the KnowledgeBase for Self-Healer audit trail suitability."""
    print("\n🤖 Evaluating KnowledgeBase for Self-Healer Integration...")
    print("=" * 60)
    
    try:
        db_tool = MCPDatabaseTool('knowledgebase')
        schema = await db_tool.get_database_schema()
        
        if not schema:
            print("❌ Could not retrieve schema for evaluation")
            return
        
        print(f"📊 Database Evaluation Results:")
        print(f"   Database: {schema.database_name}")
        print(f"   Total Tables: {len(schema.tables)}")
        print(f"   Total Views: {len(schema.views)}")
        print(f"   Total Procedures: {len(schema.procedures)}")
        
        # Look for existing relevant tables
        relevant_tables = []
        for table in schema.tables:
            table_name = table['name'].lower()
            if any(keyword in table_name for keyword in ['error', 'log', 'audit', 'session', 'knowledge', 'learning']):
                relevant_tables.append(table)
        
        if relevant_tables:
            print(f"\n🎯 Potentially Relevant Existing Tables:")
            for table in relevant_tables:
                print(f"   • {table['schema']}.{table['name']}")
        
        # Recommendations
        print(f"\n💡 Self-Healer Integration Recommendations:")
        print(f"   ✅ Database is suitable for Self-Healer audit trail")
        print(f"   ✅ SQL Server 2017 supports all required features")
        print(f"   ✅ Trusted connection working properly")
        
        if relevant_tables:
            print(f"   🔍 Consider extending existing tables: {len(relevant_tables)} found")
        else:
            print(f"   🆕 Recommend creating new Self-Healer specific tables")
        
        print(f"   📋 Suggested new tables:")
        print(f"      • SelfHealer_ErrorSessions - Main error session tracking")
        print(f"      • SelfHealer_ErrorDetails - Detailed error information")
        print(f"      • SelfHealer_Solutions - Solution attempts and outcomes")
        print(f"      • SelfHealer_LearningRecords - Learning and pattern data")
        print(f"      • SelfHealer_AuditTrail - Complete audit trail")
        
        return True
        
    except Exception as e:
        print(f"❌ Evaluation Failed: {e}")
        return False


async def main():
    """Run all MCP database tests."""
    print("🚀 MCP Database Tool Test Suite")
    print("Testing SQL Server KnowledgeBase Connection")
    print("=" * 60)
    
    # Test 1: Basic connection
    connection_success = await test_database_connection()
    if not connection_success:
        print("\n❌ Connection failed - stopping tests")
        return
    
    # Test 2: Schema retrieval
    schema = await test_database_schema()
    if not schema:
        print("\n❌ Schema retrieval failed - stopping tests")
        return
    
    # Test 3: Table analysis
    await test_table_analysis(schema)
    
    # Test 4: Sample queries
    await test_sample_queries(schema)
    
    # Test 5: Self-Healer evaluation
    await evaluate_for_self_healer()
    
    print("\n🎉 MCP Database Test Suite Complete!")
    print("=" * 60)
    print("✅ SQL Server KnowledgeBase is ready for Self-Healer integration!")


if __name__ == "__main__":
    asyncio.run(main())
