"""
Project Cleanup Manager for N8N Builder
Implements safe cleanup strategies to reduce file count from 5000+ to manageable levels
"""

import os
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from datetime import datetime
import argparse


class ProjectCleanupManager:
    """
    Manages safe cleanup of the N8N Builder project to reduce file accumulation.
    Uses analysis data to identify and safely remove/archive unnecessary files.
    """
    
    def __init__(self, project_root: Optional[Path] = None, analysis_file: Optional[Path] = None):
        """Initialize the cleanup manager."""
        self.project_root = project_root or Path.cwd()
        self.analysis_file = analysis_file or Path("Scripts/project_analysis_report.json")
        
        # Cleanup configuration
        self.archive_dir = self.project_root / "Archive"
        self.backup_dir = self.project_root / "Backup"
        
        # Safety settings
        self.dry_run = True  # Default to dry run for safety
        self.max_files_per_operation = 100  # Limit batch operations
        self.require_confirmation = True
        
        # Tracking
        self.cleanup_log = []
        self.files_moved = 0
        self.files_deleted = 0
        self.space_saved = 0
        
        # Logger
        self.logger = logging.getLogger('project_cleanup')
        self._setup_logging()
        
        # Load analysis data
        self.analysis_data = None
        self._load_analysis_data()
    
    def _setup_logging(self):
        """Set up logging for cleanup operations."""
        log_file = Path("logs/project_cleanup.log")
        log_file.parent.mkdir(exist_ok=True)
        
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def _load_analysis_data(self):
        """Load project analysis data."""
        try:
            if self.analysis_file.exists():
                with open(self.analysis_file, 'r', encoding='utf-8') as f:
                    self.analysis_data = json.load(f)
                self.logger.info(f"Loaded analysis data: {self.analysis_data['summary']}")
            else:
                self.logger.warning(f"Analysis file not found: {self.analysis_file}")
        except Exception as e:
            self.logger.error(f"Failed to load analysis data: {e}")
    
    def create_archive_structure(self):
        """Create archive directory structure for safe file storage."""
        archive_dirs = [
            self.archive_dir / "duplicates",
            self.archive_dir / "obsolete", 
            self.archive_dir / "cache",
            self.archive_dir / "temp",
            self.archive_dir / "logs",
            self.backup_dir
        ]
        
        for dir_path in archive_dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created archive directory: {dir_path}")
        
        print("📁 Archive structure created:")
        for dir_path in archive_dirs:
            print(f"   {dir_path}")
    
    def cleanup_cache_directories(self):
        """Clean up cache directories and temporary files."""
        print("\n🧹 Cleaning up cache directories...")
        
        cache_patterns = [
            "__pycache__",
            ".pytest_cache", 
            "*.pyc",
            "*.pyo",
            ".coverage",
            "htmlcov",
            ".tox",
            ".mypy_cache",
            "node_modules",
            ".npm",
            ".cache"
        ]
        
        files_cleaned = 0
        space_saved = 0
        
        for pattern in cache_patterns:
            if pattern.startswith("*"):
                # File pattern
                for file_path in self.project_root.rglob(pattern):
                    if file_path.is_file():
                        size = file_path.stat().st_size
                        if not self.dry_run:
                            file_path.unlink()
                            self.logger.info(f"Deleted cache file: {file_path}")
                        else:
                            self.logger.info(f"Would delete cache file: {file_path}")
                        
                        files_cleaned += 1
                        space_saved += size
            else:
                # Directory pattern
                for dir_path in self.project_root.rglob(pattern):
                    if dir_path.is_dir():
                        size = sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file())
                        if not self.dry_run:
                            shutil.rmtree(dir_path)
                            self.logger.info(f"Deleted cache directory: {dir_path}")
                        else:
                            self.logger.info(f"Would delete cache directory: {dir_path}")
                        
                        files_cleaned += 1
                        space_saved += size
        
        print(f"   Cache cleanup: {files_cleaned} items, {space_saved:,} bytes saved")
        self.files_deleted += files_cleaned
        self.space_saved += space_saved
    
    def archive_duplicate_files(self):
        """Archive duplicate files, keeping the best version."""
        if not self.analysis_data or not self.analysis_data.get('duplicates'):
            print("⚠️ No duplicate file data available")
            return
        
        print("\n📦 Archiving duplicate files...")
        
        duplicates = self.analysis_data['duplicates']
        files_info = self.analysis_data['files']
        
        for file_hash, file_list in duplicates.items():
            if len(file_list) <= 1:
                continue
            
            # Determine which file to keep (prefer entry points, then newest)
            best_file = None
            best_score = -1
            
            for file_path in file_list:
                if file_path not in files_info:
                    continue
                
                info = files_info[file_path]
                score = 0
                
                # Prefer entry points
                if info.get('is_entry_point', False):
                    score += 1000
                
                # Prefer newer files
                try:
                    modified = datetime.fromisoformat(info['modified'])
                    score += modified.timestamp() / 1000000  # Small weight for recency
                except:
                    pass
                
                # Prefer files in main directories
                if not any(skip in file_path for skip in ['test', 'backup', 'archive', 'temp']):
                    score += 100
                
                if score > best_score:
                    best_score = score
                    best_file = file_path
            
            # Archive the duplicates (except the best one)
            for file_path in file_list:
                if file_path == best_file:
                    continue
                
                source_path = self.project_root / file_path
                if not source_path.exists():
                    continue
                
                # Create unique archive name
                archive_name = f"{Path(file_path).stem}_{file_hash[:8]}{Path(file_path).suffix}"
                archive_path = self.archive_dir / "duplicates" / archive_name
                
                if not self.dry_run:
                    shutil.move(str(source_path), str(archive_path))
                    self.logger.info(f"Archived duplicate: {file_path} -> {archive_path}")
                else:
                    self.logger.info(f"Would archive duplicate: {file_path} -> {archive_path}")
                
                self.files_moved += 1
                if file_path in files_info:
                    self.space_saved += files_info[file_path].get('size', 0)
        
        print(f"   Duplicates archived: {self.files_moved} files")
    
    def archive_obsolete_files(self, max_files: int = 100):
        """Archive potentially obsolete files."""
        if not self.analysis_data or not self.analysis_data.get('obsolete_files'):
            print("⚠️ No obsolete file data available")
            return
        
        print(f"\n🗑️ Archiving obsolete files (max {max_files})...")
        
        obsolete_files = self.analysis_data['obsolete_files']
        files_info = self.analysis_data['files']
        
        # Sort by size (largest first) to maximize space savings
        obsolete_with_size = []
        for file_path in obsolete_files:
            if file_path in files_info:
                size = files_info[file_path].get('size', 0)
                obsolete_with_size.append((file_path, size))
        
        obsolete_with_size.sort(key=lambda x: x[1], reverse=True)
        
        archived_count = 0
        for file_path, size in obsolete_with_size[:max_files]:
            source_path = self.project_root / file_path
            if not source_path.exists():
                continue
            
            # Create archive path maintaining directory structure
            relative_path = Path(file_path)
            archive_path = self.archive_dir / "obsolete" / relative_path
            archive_path.parent.mkdir(parents=True, exist_ok=True)
            
            if not self.dry_run:
                shutil.move(str(source_path), str(archive_path))
                self.logger.info(f"Archived obsolete: {file_path} -> {archive_path}")
            else:
                self.logger.info(f"Would archive obsolete: {file_path} -> {archive_path}")
            
            archived_count += 1
            self.files_moved += 1
            self.space_saved += size
        
        print(f"   Obsolete files archived: {archived_count} files")
    
    def cleanup_large_log_files(self, max_size_mb: int = 10):
        """Archive large log files."""
        print(f"\n📋 Archiving large log files (>{max_size_mb}MB)...")
        
        log_dirs = [
            self.project_root / "logs",
            self.project_root / "n8n-docker" / "logs",
            self.project_root / "Self_Healer" / "logs"
        ]
        
        archived_count = 0
        for log_dir in log_dirs:
            if not log_dir.exists():
                continue
            
            for log_file in log_dir.rglob("*.log"):
                if log_file.is_file():
                    size_mb = log_file.stat().st_size / (1024 * 1024)
                    if size_mb > max_size_mb:
                        # Archive with timestamp
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        archive_name = f"{log_file.stem}_{timestamp}{log_file.suffix}"
                        archive_path = self.archive_dir / "logs" / archive_name
                        
                        if not self.dry_run:
                            shutil.move(str(log_file), str(archive_path))
                            self.logger.info(f"Archived large log: {log_file} -> {archive_path}")
                        else:
                            self.logger.info(f"Would archive large log: {log_file} -> {archive_path}")
                        
                        archived_count += 1
                        self.files_moved += 1
                        self.space_saved += log_file.stat().st_size
        
        print(f"   Large logs archived: {archived_count} files")
    
    def generate_cleanup_report(self):
        """Generate a report of cleanup operations."""
        report = {
            'cleanup_date': datetime.now().isoformat(),
            'dry_run': self.dry_run,
            'summary': {
                'files_moved': self.files_moved,
                'files_deleted': self.files_deleted,
                'space_saved_bytes': self.space_saved,
                'space_saved_mb': round(self.space_saved / (1024 * 1024), 2)
            },
            'operations': self.cleanup_log
        }
        
        report_file = Path("logs/project_cleanup_report.json")
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📊 Cleanup Report:")
        print(f"   Files moved to archive: {self.files_moved}")
        print(f"   Files deleted: {self.files_deleted}")
        print(f"   Space saved: {self.space_saved:,} bytes ({self.space_saved/(1024*1024):.2f} MB)")
        print(f"   Report saved to: {report_file}")
    
    def run_safe_cleanup(self, operations: List[str] = None):
        """Run safe cleanup operations."""
        if operations is None:
            operations = ['cache', 'duplicates', 'obsolete', 'logs']
        
        print("🧹 N8N Builder Project Cleanup")
        print("=" * 50)
        print(f"Mode: {'DRY RUN' if self.dry_run else 'LIVE'}")
        print(f"Project root: {self.project_root}")
        print()
        
        # Create archive structure
        self.create_archive_structure()
        
        # Run cleanup operations
        if 'cache' in operations:
            self.cleanup_cache_directories()
        
        if 'duplicates' in operations:
            self.archive_duplicate_files()
        
        if 'obsolete' in operations:
            self.archive_obsolete_files()
        
        if 'logs' in operations:
            self.cleanup_large_log_files()
        
        # Generate report
        self.generate_cleanup_report()
        
        if self.dry_run:
            print("\n⚠️ This was a DRY RUN - no files were actually moved or deleted")
            print("   Run with --execute to perform actual cleanup")


def main():
    """Main entry point for project cleanup."""
    parser = argparse.ArgumentParser(description="N8N Builder Project Cleanup Manager")
    parser.add_argument('--execute', action='store_true', help='Execute cleanup (default is dry run)')
    parser.add_argument('--operations', nargs='+', choices=['cache', 'duplicates', 'obsolete', 'logs'],
                       default=['cache', 'duplicates', 'obsolete', 'logs'],
                       help='Cleanup operations to perform')
    parser.add_argument('--max-obsolete', type=int, default=100,
                       help='Maximum obsolete files to archive in one run')
    
    args = parser.parse_args()
    
    # Initialize cleanup manager
    cleanup_manager = ProjectCleanupManager()
    cleanup_manager.dry_run = not args.execute
    
    # Run cleanup
    cleanup_manager.run_safe_cleanup(args.operations)


if __name__ == "__main__":
    main()
