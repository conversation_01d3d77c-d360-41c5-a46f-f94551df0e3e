#!/usr/bin/env python3
"""
Debug script to trace the complete Self-Healer error processing flow.
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

async def debug_self_healer_flow():
    print("=== DEBUGGING SELF-HEALER ERROR PROCESSING FLOW ===")
    
    try:
        from Self_Healer.core.error_monitor import ErrorMonitor
        from Self_Healer.core.healer_manager import SelfHealerManager
        
        # Initialize error monitor
        log_directory = Path("logs")
        error_monitor = ErrorMonitor(log_directory)
        
        print(f"\n1. ERROR MONITOR INITIALIZATION:")
        print(f"   - Log directory: {log_directory}")
        print(f"   - Error log path: {error_monitor.error_log_path}")
        print(f"   - Main log path: {error_monitor.main_log_path}")
        print(f"   - Error patterns loaded: {len(error_monitor.error_patterns)}")
        
        # Check detected errors
        print(f"\n2. CURRENTLY DETECTED ERRORS:")
        print(f"   - Total detected errors: {len(error_monitor.detected_errors)}")
        
        if error_monitor.detected_errors:
            print("   - Error details:")
            for error_id, detected_error in error_monitor.detected_errors.items():
                time_since = datetime.now() - detected_error.timestamp
                print(f"     * {error_id[:8]}: {detected_error.error_detail.title}")
                print(f"       - Severity: {detected_error.severity}")
                print(f"       - Category: {detected_error.category}")
                print(f"       - Frequency: {detected_error.frequency}")
                print(f"       - Age: {time_since.total_seconds():.1f} seconds")
                print(f"       - Message: {detected_error.error_detail.message[:100]}...")
        
        # Test get_new_errors criteria
        print(f"\n3. NEW ERRORS CRITERIA TEST:")
        new_errors = await error_monitor.get_new_errors()
        print(f"   - Errors meeting 'new' criteria: {len(new_errors)}")
        
        if new_errors:
            print("   - New errors for healing:")
            for error in new_errors:
                print(f"     * {error.title}: {error.message[:80]}...")
        else:
            print("   - No errors meet the criteria for healing")
            print("   - Criteria analysis:")
            current_time = datetime.now()
            
            for error_id, detected_error in error_monitor.detected_errors.items():
                time_since_detection = current_time - detected_error.timestamp
                is_recent = time_since_detection.total_seconds() < 3600  # 1 hour
                is_recurring = detected_error.frequency > 1
                is_critical = detected_error.severity in ['CRITICAL', 'ERROR']
                is_validation_error = 'validation' in detected_error.category.lower()
                is_workflow_error = 'workflow' in detected_error.category.lower()
                
                error_message = detected_error.error_detail.message.lower()
                is_validation_in_message = any(keyword in error_message for keyword in ['validation', 'trigger node', 'workflow failed'])
                
                print(f"     * {error_id[:8]}:")
                print(f"       - Recent (<1hr): {is_recent} ({time_since_detection.total_seconds():.1f}s)")
                print(f"       - Critical/Error: {is_critical} ({detected_error.severity})")
                print(f"       - Recurring: {is_recurring} (freq: {detected_error.frequency})")
                print(f"       - Validation error: {is_validation_error}")
                print(f"       - Workflow error: {is_workflow_error}")
                print(f"       - Validation in message: {is_validation_in_message}")
                
                meets_criteria = ((is_recent and is_critical) or 
                                (is_recurring and is_recent) or 
                                (is_validation_error and is_recent) or 
                                (is_workflow_error and is_recent) or 
                                (is_validation_in_message and is_critical))
                print(f"       - MEETS CRITERIA: {meets_criteria}")
        
        # Test Self-Healer manager integration
        print(f"\n4. SELF-HEALER MANAGER TEST:")
        try:
            healer = SelfHealerManager()
            print(f"   - SelfHealerManager initialized: ✅")
            print(f"   - Error monitor connected: {healer.error_monitor is not None}")
            print(f"   - Is running: {healer.is_running}")
            
            if not healer.is_running:
                print("   - Self-Healer is not running - this explains why no healing occurs!")
                print("   - The Self-Healer needs to be started to process errors")
        except Exception as e:
            print(f"   - Failed to initialize SelfHealerManager: {e}")
        
        # Check recent log entries
        print(f"\n5. RECENT LOG ANALYSIS:")
        error_log_path = Path("logs/errors.log")
        if error_log_path.exists():
            with open(error_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                recent_lines = lines[-10:]  # Last 10 lines
                
            print(f"   - Total lines in error log: {len(lines)}")
            print(f"   - Last 10 lines:")
            for i, line in enumerate(recent_lines, len(lines) - 10 + 1):
                print(f"     {i:3d}: {line.strip()}")
        
        print(f"\n6. SUMMARY:")
        print(f"   - Errors are being detected: {'✅' if error_monitor.detected_errors else '❌'}")
        print(f"   - Errors meet healing criteria: {'✅' if new_errors else '❌'}")
        print(f"   - Self-Healer manager exists: {'✅' if 'healer' in locals() else '❌'}")
        print(f"   - Self-Healer is running: {'✅' if 'healer' in locals() and healer.is_running else '❌'}")
        
        if not new_errors and error_monitor.detected_errors:
            print(f"\n   🔍 DIAGNOSIS: Errors detected but don't meet healing criteria")
            print(f"      - Consider relaxing criteria in get_new_errors() method")
            print(f"      - Or ensure Self-Healer is properly started")
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_self_healer_flow())
