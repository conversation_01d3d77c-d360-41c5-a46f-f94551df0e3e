#!/usr/bin/env python3
"""
Complete Self-Healer Flow Test
=============================
Tests the complete n8n_builder -> error -> self-healer -> knowledgebase flow
with a continuously running Self-Healer instance.
"""

import asyncio
import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger('complete_flow_test')

async def test_complete_self_healer_flow():
    """Test the complete Self-Healer flow with continuous operation."""
    
    print("=" * 80)
    print("COMPLETE SELF-HEALER FLOW TEST")
    print("Testing: n8n_builder -> error -> self-healer -> knowledgebase")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print()
    
    # Step 1: Initialize and start Self-Healer (keep it running)
    print("STEP 1: INITIALIZING CONTINUOUS SELF-HEALER")
    print("-" * 60)
    
    try:
        from Self_Healer.core.healer_manager import SelfHealerManager
        
        # Initialize healer manager
        healer_manager = SelfHealerManager()
        print("✅ SelfHealerManager initialized")
        
        # Start the healer manager
        await healer_manager.start()
        print("✅ SelfHealerManager started and monitoring")
        print(f"   Is running: {healer_manager.is_running}")
        print(f"   Status: {healer_manager.status}")
        
        # Force initial error scan
        print("\n🔍 Forcing initial error scan...")
        new_errors_found = await healer_manager.error_monitor.force_rescan_logs(hours_back=2)
        print(f"   Found {new_errors_found} errors during initial scan")
        
        initial_status = await healer_manager.get_status()
        print(f"   Total errors detected: {initial_status['metrics']['total_errors_detected']}")
        
    except Exception as e:
        print(f"❌ Failed to initialize Self-Healer: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Step 2: Simulate error generation (like n8n_builder would do)
    print(f"\nSTEP 2: SIMULATING ERROR GENERATION")
    print("-" * 60)
    
    try:
        # Add a test error to the error log to simulate n8n_builder generating an error
        error_log_path = Path("logs/errors.log")
        test_error_message = f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]}] ERROR test_module: Test error for Self-Healer validation - workflow failed validation"
        
        with open(error_log_path, 'a') as f:
            f.write(f"\n{test_error_message}\n")
        
        print("✅ Simulated error written to error log")
        print(f"   Error: {test_error_message}")
        
    except Exception as e:
        print(f"❌ Failed to simulate error: {e}")
    
    # Step 3: Wait for Self-Healer to detect and process the error
    print(f"\nSTEP 3: MONITORING SELF-HEALER RESPONSE")
    print("-" * 60)
    
    print("⏳ Waiting 15 seconds for Self-Healer to detect and process errors...")
    
    for i in range(15):
        await asyncio.sleep(1)
        
        if i % 5 == 0:  # Check every 5 seconds
            try:
                current_status = await healer_manager.get_status()
                active_sessions = len(healer_manager.active_sessions)
                total_errors = current_status['metrics']['total_errors_detected']
                healing_attempts = current_status['metrics']['total_healing_attempts']
                
                print(f"   [{i+1}/15] Errors: {total_errors}, Active Sessions: {active_sessions}, Healing Attempts: {healing_attempts}")
                
                # Check if we have any healing activity
                if healing_attempts > 0:
                    print("   🎉 SUCCESS: Self-Healer is processing errors!")
                    break
                    
            except Exception as e:
                print(f"   ⚠️  Error checking status: {e}")
    
    # Step 4: Final status and analysis
    print(f"\nSTEP 4: FINAL ANALYSIS")
    print("-" * 60)
    
    try:
        final_status = await healer_manager.get_status()
        
        print(f"📊 Final Self-Healer Status:")
        print(f"   System Status: {final_status['status']}")
        print(f"   Is Running: {final_status['is_running']}")
        print(f"   Total Errors Detected: {final_status['metrics']['total_errors_detected']}")
        print(f"   Total Healing Attempts: {final_status['metrics']['total_healing_attempts']}")
        print(f"   Success Rate: {final_status['metrics']['success_rate']:.1f}%")
        print(f"   Active Sessions: {len(healer_manager.active_sessions)}")
        print(f"   Session History: {len(healer_manager.session_history)}")
        
        # Check error monitor directly
        error_monitor = healer_manager.error_monitor
        new_errors = await error_monitor.get_new_errors()
        
        print(f"\n🔍 Error Monitor Analysis:")
        print(f"   Total detected errors: {len(error_monitor.detected_errors)}")
        print(f"   Errors meeting healing criteria: {len(new_errors)}")
        
        if new_errors:
            print(f"   Errors ready for healing:")
            for i, error in enumerate(new_errors[:3]):
                print(f"     {i+1}. {error.title}: {error.message[:60]}...")
        
        # Determine success
        success = (
            final_status['metrics']['total_errors_detected'] > 0 and
            len(new_errors) > 0
        )
        
        if success:
            print(f"\n✅ SUCCESS: Complete Self-Healer flow is working!")
            print(f"   - Errors are being detected")
            print(f"   - Errors meet healing criteria")
            print(f"   - Self-Healer is ready to process them")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: Some components working, others need attention")
            
        # Test knowledge base integration
        print(f"\n🔍 Testing Knowledge Base Integration:")
        try:
            knowledge_integrator = healer_manager.knowledge_integrator
            if knowledge_integrator:
                print(f"   ✅ Knowledge Base integrator available")
            else:
                print(f"   ⚠️  Knowledge Base integrator not available")
        except Exception as e:
            print(f"   ❌ Knowledge Base integration error: {e}")
        
    except Exception as e:
        print(f"❌ Error in final analysis: {e}")
        success = False
    
    # Step 5: Cleanup
    print(f"\nSTEP 5: CLEANUP")
    print("-" * 60)
    
    try:
        await healer_manager.stop()
        print("✅ Self-Healer stopped successfully")
    except Exception as e:
        print(f"❌ Error stopping Self-Healer: {e}")
    
    # Summary
    print(f"\n" + "=" * 80)
    print("COMPLETE FLOW TEST SUMMARY")
    print("=" * 80)
    
    if success:
        print("🎉 OVERALL RESULT: SUCCESS")
        print("\nThe Self-Healer system is working correctly:")
        print("✅ Error detection is working")
        print("✅ Error processing criteria are working")
        print("✅ Self-Healer manager is operational")
        print("✅ Components are properly integrated")
        
        print(f"\n🚀 NEXT STEPS:")
        print("1. Start the full system with 'python run.py'")
        print("2. Generate workflows to trigger real errors")
        print("3. Monitor the dashboard at http://localhost:8081")
        print("4. Verify healing actions are logged to the knowledgebase")
        
    else:
        print("⚠️  OVERALL RESULT: NEEDS ATTENTION")
        print("\nSome issues remain:")
        print("- Check error detection and processing")
        print("- Verify Self-Healer startup in main application")
        print("- Test with actual workflow generation errors")
    
    print(f"\nTest completed at: {datetime.now()}")
    return success

if __name__ == "__main__":
    success = asyncio.run(test_complete_self_healer_flow())
    exit_code = 0 if success else 1
    sys.exit(exit_code)
