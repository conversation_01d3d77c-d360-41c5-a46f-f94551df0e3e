import asyncio
from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def validate_stored_procedures():
    db_tool = MCPDatabaseTool('knowledgebase')
    
    print("=== VALIDATING STORED PROCEDURES ===")
    
    # Check what stored procedures exist
    print("\n1. Checking existing stored procedures:")
    try:
        existing_procs = await db_tool.execute_query("""
        SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
        """)
        
        if existing_procs.get('rows'):
            print(f"Found {len(existing_procs['rows'])} Self-Healer procedures:")
            for proc in existing_procs['rows']:
                print(f"  - {proc['name']} (created: {proc['create_date']})")
        else:
            print("❌ No Self-Healer stored procedures found!")
            
    except Exception as e:
        print(f"Error checking procedures: {e}")
    
    # Test if our specific procedure exists
    print("\n2. Testing S_SYS_SelfHealer_RecentSessions_P:")
    try:
        test_result = await db_tool.execute_stored_procedure(
            'S_SYS_SelfHealer_RecentSessions_P',
            {'Limit': 1}
        )
        print(f"✅ Procedure exists and works! Result: {test_result}")
    except Exception as e:
        print(f"❌ Procedure test failed: {e}")
        
        # Try to create it
        print("\n3. Creating S_SYS_SelfHealer_RecentSessions_P:")
        create_sql = """
        CREATE PROCEDURE S_SYS_SelfHealer_RecentSessions_P
            @Limit INT = 10
        AS
        BEGIN
            SET NOCOUNT ON;
            
            SELECT TOP (@Limit)
                e.ID as EntityID,
                REPLACE(e.Name, 'Session_', '') as SessionID,
                e.CreateDate,
                'Unknown' as Duration,
                'Unknown' as SuccessRate,
                'Resolved' as Status,
                1 as Success
            FROM REF_Entities e
            WHERE e.Name LIKE 'Session_%'
            ORDER BY e.CreateDate DESC
        END
        """
        
        try:
            await db_tool.execute_query(create_sql)
            print("✅ Procedure created successfully!")
            
            # Test it again
            test_result = await db_tool.execute_stored_procedure(
                'S_SYS_SelfHealer_RecentSessions_P',
                {'Limit': 1}
            )
            print(f"✅ Procedure test after creation: {test_result}")
            
        except Exception as create_error:
            print(f"❌ Failed to create procedure: {create_error}")
    
    # Final validation - list all procedures again
    print("\n4. Final validation - all procedures:")
    try:
        final_procs = await db_tool.execute_query("""
        SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
        """)
        
        if final_procs.get('rows'):
            print("Current Self-Healer procedures:")
            for proc in final_procs['rows']:
                print(f"  ✅ {proc['name']}")
        else:
            print("❌ Still no Self-Healer procedures found!")
            
    except Exception as e:
        print(f"Error in final validation: {e}")

if __name__ == "__main__":
    asyncio.run(validate_stored_procedures())
